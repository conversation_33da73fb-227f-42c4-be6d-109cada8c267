<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry excluding="*.js|app/app-js/app-lib/angular-animate.js|app/app-js/app-lib/angular-cookies.js|app/app-js/app-lib/angular-material.min.js|app/app-js/app-lib/angular-messages.js|app/app-js/app-lib/angular-route.js|app/app-js/app-lib/angular.js|app/app-js/app-lib/bootstrap-timepicker.js|app/app-js/app-lib/bootstrap.min.js|app/app-js/app-lib/intlTelInput.min.js|app/app-js/app-lib/jquery-3.1.1.min.js|app/app-js/app-lib/jquery-ui.js|app/app-js/app-lib/jquery.dialogBox.js|app/app-js/app-lib/jquery.min.js|app/app-js/app-lib/lodash.js|app/app-js/app-lib/lodash.min.js|app/app-js/app-lib/ng-intl-tel-input.js|app/app-js/app-lib/ng-table.min.js|app/app-js/app-lib/timepicki.js|app/app-js/app-lib/utils.js|resources/js/datatable/pdfmake.min.js|resources/js/jasny-bootstrap.js|vendors/ckeditor/ckeditor.js" kind="src" path="src/main/webapp"/>
	<classpathentry excluding="*.js" kind="src" path="target/m2e-wtp/web-resources"/>
	<classpathentry kind="con" path="org.eclipse.wst.jsdt.launching.JRE_CONTAINER"/>
	<classpathentry kind="con" path="org.eclipse.wst.jsdt.launching.WebProject">
		<attributes>
			<attribute name="hide" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.wst.jsdt.launching.baseBrowserLibrary"/>
	<classpathentry kind="output" path=""/>
</classpath>
