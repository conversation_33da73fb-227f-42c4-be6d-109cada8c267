image: google/cloud-sdk:slim

pipelines:
  branches:
    audioSummarizer:
      - step:
          name: Deploy to App Engine (prod)
          caches:
            - maven
          script:
            - apt-get update && apt-get install -y maven
            - echo "$GCLOUD_KEY_FILE" | base64 --decode > ${HOME}/gcloud-key.json
            - gcloud auth activate-service-account --key-file=${HOME}/gcloud-key.json
            - gcloud config set project $GCLOUD_PROJECT
            - mvn clean package || (echo "Maven build failed!" && exit 1)
            - mvn clean package appengine:deploy

  pull-requests:
    '**':
      - step:
          name: Test Build (PR)
          caches:
            - maven
          script:
            - apt-get update && apt-get install -y maven
            - mvn clean verify
