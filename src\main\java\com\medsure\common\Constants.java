package com.medsure.common;

/**
 * The Interface Constants.
 * 
 * <AUTHOR>
 */
public interface Constants {

	interface ServingURL {
		// final String URL = "https://7-dot-watchrx-1007.appspot.com";
		 final String URL = "https://8-dot-watchrx-1007.appspot.com";
		// final String URL = "https://10-dot-watchrx-1007.appspot.com";
		//final String URL = "https://ccmrpm.caremountain.com";
	}

	interface AppColor {
		final Integer BLUE = 2;
	}

	interface AppLanguage {
		final Integer ENGLISH = 1;
	}

	interface Email {
		final String ADMINEMAIL = "<EMAIL>";
	}

	interface Admin {
		final Long ADMINID = 9L;
	}

	interface ApiKey {
		final String API_KEY = "AIzaSyDk4WEfovabzLuh8my2OiUUtdeQt9XFvPs";

	}

	interface Url {
		final String URL = "https://maps.googleapis.com/maps/api/geocode/json";
	}

	interface GPSStatus {
		final String ENABLED = "E";
		final String DISABLED = "D";
		final String TRACKING_ENABLED = "TE";
		final String TRACKING_DISABLED = "TD";
	}

	interface Store {
		final Integer DELIVERYCOST = 1000;
		final Double TAXPERCENT = 6.5;
	}

	interface Status {
		final String ACTIVE = "Y";
		final String INACTIVE = "N";
	}

	interface CaregiverRole {
		final String PRIMARY = "P";
		final String SECONDARY = "S";
	}

	/*
	 * This constant is to set the assign/ unassign status of the watch for the
	 * patient
	 */
	interface WatchAssignStatus {
		final String ASSIGNED = "A";
		final String UNASSIGNED = "U";
	}

	interface WatchAllUnAllStatus {
		final String ALLOCATED = "ALLOCATED";
		final String UNALLOCATED = "UNALLOCATED";
	}

	interface SecCGStatus {
		final String ACCEPT = "A";
		final String REJECT = "R";
		final String INVITE = "I";
	}

	interface UserType {
		final Integer ADMIN = 1;
		final Integer COORDINATOR = 2;
		final Integer PHYSICIAN = 3;
		final Integer CAREGIVER = 4;
		final Integer CASEMANAGER = 5;
		final Integer PATIENT = 6;
	}

	interface WatchType {
		final String CUSTOM = "C";
		final String INVENTORY = "I";

	}

	interface ReferenceType {
		final String SHIFT = "SHIFT";
		final String MARITAL_STATUS = "MARITAL_STATUS";
		final String LANGUAGE = "LANGUAGE";
		final String SPECIALITY = "SPECIALITY";
		final String CLINICIAN_TYPE = "CLINICIAN_TYPE";
		final String MEDICINE_FORM = "MEDICINE_FORM";
		final String MEDICINE_LIST = "MEDICINE_LIST";
		final String ORDER_STATUS = "ORDER_STATUS";
		final String APP_COLOR = "APP_COLOR";

	}

	interface Shift {
		final int MORNIG = 1;
		final int NOON = 2;
		final int NIGHT = 3;
	}

	interface CareGiverShiftTime {
		final String MORNING = "08:01-16:00";
		final String NOON = "16:01-24:00";
		final String NIGHT = "00:01-08:00";
	}

	interface Stripe {
		final String SECRETKEY = "sk_test_w9ivZkHBPGwafFF4tOQQUoRr";
		final String PUBLISHABLEKEY = "pk_test_tuVmOR8RMqBako2N5XpdOp1U";

	}

	interface LineItemType {
		final Integer WatchPrice = 1;
		final Integer WatchDiscountAmount = 2;
		final Integer WatchDiscountPercent = 3;
		final Integer WatchTax = 4;
		final Integer WatchPlan = 5;
		final Integer WatchPlanDiscountAmount = 6;
		final Integer WatchPlanDiscountPercent = 7;
		final Integer DeliveryCharge = 8;
	}

	interface LineItemDescription {
		final String DeliveryCharge = "Delivery Charge";
		final String WatchDiscountAmount = "Watch Discount Amount";
		final String WatchDiscountPercent = "Watch Discount Percent";
		final String WatchPrice = "Watch Price";
		final String WatchTax = "Watch Tax";
		final String WatchPlanDiscountAmount = "Plan Discount Amount";
		final String WatchPlanDiscountPercent = "Plan Discount Percent";
	}

	interface OrderStatus {
		final Integer Pending = 1;
		final Integer Shipped = 2;
		final Integer RefundInitiated = 3;
		final Integer Cancelled = 4;
		final Integer PaymentFailed = 5;
		final Integer PaymentSucceded = 6;
		final Integer OrderBeingPrepared = 7;
		final Integer RefundFailed = 8;
		final Integer RefundSuccess = 9;
		final Integer Delivered = 10;
		final Integer Returned = 11;
		final Integer PendingReturn = 12;
	}

	interface OrderStatusDescription {
		final String Pending = "Pending";
		final String Shipped = "Shipped";
		final String RefundInitiated = "Refund Initiated";
		final String Cancelled = "Cancelled";
		final String PaymentFailed = "Payment Failed";
		final String PaymentSucceded = "Payment Succeded";
		final String OrderBeingPrepared = "Order Being Prepared";
		final String RefundFailed = "Refund Failed";
		final String RefundSuccess = "Refund Success";
		final String Delivered = "Delivered";
		final String Returned = "Returned";
		final String PendingReturn = "Pending Return";
	}

	interface ResponseCode {
		final int LOGINSUCCESS = 1;
		final int LOGINFAIL = 2;
		final int UNAUTHORIZEDACCESS = 3;
		final int INTERNALERROR = 4;
		final int INVENTORYITEMSADDEDSUCCESSFULLY = 5;
		final int INVENTORYITEMSEDITEDSUCCESSFULLY = 6;
		final int INVENTORYITEMSOBTAINEDSUCCESSFULLY = 7;
		final int INVENTORYITEMSADDINGFAILED = 8;
		final int INVENTORYITEMSEDITINGFAILED = 9;
		final int CLINICIANSSOBTAINEDSUCCESSFULLY = 10;
		final int CLINICIANNOTFOUND = 11;
		final int CLINICIANSTATUSCHANGED = 12;
		final int MEDICATIONALERTSFOUND = 13;
		final int ILLEGALREQUEST = 14;
		final int FOUNDALLSOFTWAREVERSIONS = 15;
		final int SOFTWAREUPGRADESTATUSFOUND = 16;
		final int PATIENTCREATED = 17;
		final int PATIENTCREATIONFAILED = 18;
		final int CAREGIVERUNASSIGNED = 19;
		final int WATCHASSIGNED = 20;
		final int WATCHUNASSIGNED = 21;
		final int WATCHUNAVAILABLE = 22;
		final int WATCHADDED = 23;
		final int WATCHREMOVED = 24;
		final int ADDEDPARENT = 25;
		final int REMOVEDPARENT = 26;

		final int FOUNDALLUNALLOCATEDWATCHES = 27;
		final int FOUNDALLALLOCATEDWATCHES = 28;
		final int WATCHSUCCESSFULLYALLOCATED = 29;
		final int WATCHALREADYALLOCATED = 30;
		final int INSUFFICIENTARGUMENTS = 31;

		final int INVITESENT = 32;
		final int PENDINGREQUESTNOTIFICATION = 33;
		final int NOPENDINGNOTIFICATION = 34;
		final int INVITEACCEPTED = 35;
		final int INVITEREJECTED = 36;

		final int ALERTLISTEMPTY = 37;
		final int ASSIGNEDCAREGIVERS = 38;

		final int PATIENTNOTFOUND = 39;
		final int MEDICATIONNOTFOUND = 40;
		final int IMAGESAVED = 41;
		final int IMAGESAVEFAILED = 42;

		final int APKUPLOADEDSUCCESSFULLY = 43;
		final int APKUPLOADFAILED = 44;

		final int PRESCRIPTIONSFOUND = 45;
		final int PRESCRIPTIONDELETED = 46;
		final int PRESCRIPTIONNOTFOUND = 47;
		final int PRESCRIPTIONEDITED = 48;
		final int PRESCRIPTIONCREATED = 49;

		final int ORDERPLACEDSUCCESSFULLY = 50;
		final int InsufficientItemsInInventory = 51;
		final int StripeAuthenticationFailed = 52;
		final int StripeInvalidRequest = 53;
		final int StripeCardError = 54;
		final int StripeAPIError = 55;
		final int StripeAPIConnectionError = 56;
		final int ORDERFOUNDSUCCESSFULLY = 57;
		final int ORDERNOTFOUND = 58;
		final int ORDERCANCELLEDSUCCESSFULLY = 59;
		final int CAREGIVERPROFILEFOUND = 60;
		final int CAREGIVERPROFILEUPDATED = 61;
		final int STOREADDRESSESFOUND = 62;
		final int STOREADDRESSESMODIFIED = 63;
		final int STOREADDRESSESNOTFOUND = 64;
		final int STOREADDRESSESDELETED = 65;
		final int STOREADDRESSESCREATED = 66;
		final int CARDCREATED = 67;
		final int CARDCREATIONFAILED = 68;
		final int CARDRETRIEVEDSUCCESSFULLY = 69;
		final int CARDRETRIEVALFAILED = 70;
		final int CARDNOTFOUND = 71;
		final int CARDDELETED = 72;
		final int CARDDELETIONFAILED = 73;
		final int CARDDEFAULTSUCCESS = 74;
		final int CARDDEFAULTFAILED = 75;
		final int CARDALREADYDEFAULT = 76;
		final int PLANSRETIEVEDSUCCESSFULLY = 77;

		final int INVITESENTALREADY = 78;
		final int APKIMEILISTFOUND = 79;
		final int PUBLISHEDAPKTOWATCHES = 80;
		final int PUBLISHEDAPKTOWATCHESFAILED = 81;
		final int SUBSCRIPTIONRETIEVEDSUCCESSFULLY = 82;
		final int BILLAMOUNTCALCULATED = 83;
		final int STRIPEEVENTRECEIVALACKOWLEDGEMENT = 84;
		final int STRIPEEVENTLOGGED = 85;
		final int STRIPEEVENTHANDLED = 86;
		final int PATIENTALREADYEXISTS = 87;
		final int SUBSCRIPTIONNOTFOUND = 88;
		final int SUBSCRIPTIONCANCELED = 89;
		final int ORDERCANNOTBECANCELLED = 90;
		final int ORDERSTATECHANGED = 91;
		final int COUPONVALIDITYCHECKED = 92;
		final int WATCHINFOFOUND = 93;
		final int WATCHUPDATED = 94;
		final int WATCHNOTFOUND = 95;
		final int WATCHUNASSIGNEDSUCCESSFULLY = 96;
		final int SUBSCRIPTIONRENEWED = 97;
		final int GPSINFONOTFOUND = 98;
		final Integer GPSINFOFOUND = 99;
		final Integer GPSINFOUPDATED = 100;
		final Integer GPSUPDATEFAILED = 101;
		final Integer BULKWATCHADDED = 102;
		final Integer INVENTORYNOTFOUND = 103;
		final Integer GPSINFOSAVED = 104;
		final int GPSTRACKINGENABLED = 105;
		final int GPSTRACKINGDISABLED = 106;
		final int GPSENABLED = 107;
		final int GPSDISABLED = 108;
		final int WATCHOPTIONRETIEVEDSSUCCESSFULLY = 109;

		final int INCORRECTDATEFORMAT = 110;
		final int PATIENTSAVED = 111;
		final int WATCHINVENTORYTYPECHANGED = 112;
		final int WATCHINVENTORYTYPECHANGEFAILED = 113;
		final int VISIT_VERIFICATION = 114;

		final int UPDATENOTIFICATIONFAILED = 115;
		final int DISABLEDDISPLAYREMINDER = 116;
		final int ENABLEDDISPLAYREMINDER = 117;
	}

	interface ResponseString {
		final String WATCHUNASSIGNEDSUCCESSFULLY = "WATCH UNASSIGNED SUCCESSFULLY";
		final String WATCHNOTFOUND = "WATCH NOT FOUND";
		final String BILLAMOUNTCALCULATED = "BILL AMOUNT CALCULATED";
		final String LOGINSUCCESS = "LOGIN SUCCESS";
		final String LOGINFAIL = "Login failed, Username or password is incorrect";
		final String UNAUTHORIZEDACCESS = "User does not have access to this page";
		final String INTERNALERROR = "INTERNAL ERROR";
		final String INVENTORYITEMSADDEDSUCCESSFULLY = "INVENTORY ITEMS ADDED SUCCESSFULLY";
		final String INVENTORYITEMSEDITEDSUCCESSFULLY = "INVENTORY ITEMS EDITED SUCCESSFULLY";
		final String INVENTORYITEMSOBTAINEDSUCCESSFULLY = "INVENTORY ITEMS OBTAINED SUCCESSFULLY";
		final String INVENTORYITEMSADDINGFAILED = "INVENTORY ITEMS ADDING FAILED";
		final String INVENTORYITEMSEDITINGFAILED = "INVENTORY ITEMS EDITING FAILED";
		final String CLINICIANSSOBTAINEDSUCCESSFULLY = "CLINICIANS OBTAINED SUCCESSFULLY";
		final String CLINICIANNOTFOUND = "CLINICIAN NOT FOUND";
		final String CLINICIANSTATUSCHANGED = "CLINICIAN STATUS CHANGED";
		final String MEDICATIONALERTSFOUND = "MEDICATION ALERTS FOUND";
		final String ILLEGALREQUEST = "ILLEGAL REQUEST";
		final String FOUNDALLSOFTWAREVERSIONS = "FOUND ALL SOFTWARE VERSIONS";
		final String SOFTWAREUPGRADESTATUSFOUND = "SOFTWARE UPGRADE STATUS FOUND";
		final String CAREGIVERUNASSIGNED = "SECONDARY CAREGIVER UNASSIGNED";
		final String WATCHASSIGNED = "DEVICE ASSIGNED SUCCESSFULLY";
		final String WATCHUNASSIGNED = "DEVICE UNASSIGNED SUCCESSFULLY";
		final String ADDEDPARENT = "ADDED PARENT SUCCESSFULLY";
		final String REMOVEDPARENT = "REMOVED PARENT SUCCESSFULLY";
		final String WATCHUNAVAILABLE = "WATCHES ARE UNAVAILABLE";
		final String DEVICEUNAVAILABLE = "NO ASSIGNED MEDICAL DEVICES";
		final String WATCHADDED = "NEW DEVICE ADDED BY CAREGIVER";
		final String REMOVEDWATCH = "REMOVED DEVICE SUCCESSFULLY";
		final String PATIENTCREATED = "PATIENT CREATED";
		final String PATIENTCREATIONFAILED = "PATIENTCREATIONFAILED";
		final String INVITESENT = "SECONDARY CAREGIVER INVITE REQUEST IS SENT";
		final String PENDINGREQUESTNOTIFICATION = "PENDING REQUEST NOTIFICATION FOR SECONDARY CAREGIVER";
		final String NOPENDINGNOTIFICATION = " NO PENDING NOTIFICATION";
		final String INVITEACCEPTED = "INVITE ACCEPTED";
		final String INVITEREJECTED = "INVITE REJECTED";
		final String SAMETYPEWATCHASSIGNED = "SAME TYPE OF DEVICE ALREADY ASSIGNED TO PATIENT";

		final String FOUNDALLUNALLOCATEDWATCHES = "FOUND ALL UNALLOCATED WATCHES";
		final String FOUNDALLALLOCATEDWATCHES = "FOUND ALL ALLOCATED WATCHES";
		final String WATCHSUCCESSFULLYALLOCATED = "DEVICE SUCCESSFULLY ALLOCATED";
		final String WATCHALREADYALLOCATED = "WATCH ALREADY ALLOCATED, CHOOSE AN UNIQUE IMEI AND PHONE NUMBER";
		final String WATCHALREADYALLOCATEDBYIMEI = "WATCH ALREADY ALLOCATED, CHOOSE AN UNIQUE IMEI";

		final String INSUFFICIENTARGUMENTS = "INSUFFICIENT ARGUMENTS";
		final String ALERTLISTEMPTY = "MEDICATION ALERT LIST IS EMPTY";
		final String ASSIGNEDCAREGIVERS = "ASSIGNED CAREGIVERS LIST";
		final String IMAGEUPLOADFAILURE = "IMAGE UPLOAD FAILED";
		final String IMAGEUPLOADSUCCESS = "IMAGE UPLOAD SUCCESSFUL";

		final String PATIENTNOTFOUND = "PATIENT NOT FOUND";
		final String MEDICATIONNOTFOUND = "MEDICATION NOT FOUND";
		final String IMAGESAVED = "IMAGE SAVED";
		final String AUDIOSAVED = "AUDIO UPLOADED SUCCESSFULLY";
		final String IMAGESAVEFAILED = "IMAGE SAVE FAILED";
		final String AUDIOSAVEFAILED = "AUDIO SAVE FAILED";

		final String APKUPLOADEDSUCCESSFULLY = "APK UPLOADED SUCCESSFULLY";
		final String APKUPLOADFAILED = "APK UPLOAD FAILED";
		final String PRESCRIPTIONSFOUND = "PRESCRIPTIONS FOUND";
		final String PRESCRIPTIONDELETED = "PRESCRIPTION DELETED";
		final String PRESCRIPTIONNOTFOUND = "PRESCRIPTION NOT FOUND";
		final String PRESCRIPTIONEDITED = "PRESCRIPTION EDITED";
		final String PRESCRIPTIONCREATED = "PRESCRIPTION CREATED";
		final String CAREGIVERPROFILEFOUND = "FOUND USER PROFILE";
		final String CAREGIVERPROFILEUPDATED = "USER PROFILE UPDATED";
		final String ORDERPLACEDSUCCESSFULLY = "ORDER HAS BEEN PLACED SUCCESSFULLY";
		final String InsufficientItemsInInventory = "Not enough items in stock to place order";
		final String ORDERFOUNDSUCCESSFULLY = "ORDER FOUND SUCCESSFULLY";
		final String ORDERNOTFOUND = "ORDER NOT FOUND";
		final String ORDERCANCELLEDSUCCESSFULLY = "ORDER CANCELLED SUCCESSFULLY";
		final String INVITEALREADYSENT = "INVITE SENT ALREADY";
		final String STOREADDRESSESFOUND = "USER STORE ADDRESSES FOUND";
		final String STOREADDRESSESMODIFIED = "STORE ADDRESSES MODIFIED";
		final String STOREADDRESSESNOTFOUND = "STORE ADDRESSES NOT FOUND";
		final String STOREADDRESSESDELETED = "STORE ADDRESSES DELETED";
		final String STOREADDRESSESCREATED = "ADDRESSES SAVED SUCCESSFULLY";
		final String CARDCREATED = "CARD CREATED";
		final String CARDCREATIONFAILED = "CARD CREATION FAILED";
		final String CARDRETRIEVEDSUCCESSFULLY = "CARD RETRIEVED SUCCESSFULLY";
		final String CARDRETRIEVALFAILED = "CARD RETRIEVAL FAILED";

		final String CARDNOTFOUND = "CARD NOT FOUND";
		final String CARDDELETED = "CARD DELETED";
		final String CARDDELETIONFAILED = "CARD DELETION FAILED, MAKE SURE CARD IS NOT DEFAULT CARD USED FOR PAYMENT";
		final String CARDDEFAULTSUCCESS = "CARD DEFAULT SUCCESS";
		final String CARDDEFAULTFAILED = "CARD DEFAULT FAILED";
		final String CARDALREADYDEFAULT = "CANNOT MAKE A PRIMARY PAYMENT CARD, PRIMARY AGAIN";
		final String PLANSRETIEVEDSUCCESSFULLY = "PLANS RETIEVED SUCCESSFULLY";
		final String WATCHOPTIONRETIEVEDSSUCCESSFULLY = "WATCH OPTIONS RETIEVED SUCCESSFULLY";

		final String APKIMEILISTFOUND = "APK IMEI LIST FOUND";
		final String PUBLISHEDAPKTOWATCHES = "PUBLISHED APK TO WATCHES";
		final String PUBLISHEDAPKTOWATCHESFAILED = "PUBLISHED APK TO WATCHES FAILED";
		final String SUBSCRIPTIONRETIEVEDSUCCESSFULLY = "SUBSCRIPTION RETIEVED SUCCESSFULLY";
		final String STRIPEEVENTRECEIVALACKOWLEDGEMENT = "Stripe Event has been received by server";
		final String STRIPEEVENTLOGGED = "Stripe Event has been logged";
		final String STRIPEEVENTHANDLED = "Stripe Event has been handled";
		final String PATIENTALREADYEXISTS = "Patient Already Exists";

		final String SUBSCRIPTIONNOTFOUND = "SUBSCRIPTION NOT FOUND";
		final String SUBSCRIPTIONCANCELED = "SUBSCRIPTION CANCELED";
		final String ORDERCANNOTBECANCELLED = "ORDER CANNOT BE CANCELLED";
		final String ORDERSTATECHANGED = "ORDER STATE CHANGED";
		final String COUPONVALIDITYCHECKED = "COUPON VALIDITY CHECKED";
		final String WATCHINFO = "WATCH INFO FOUND";
		final String WATCHUPDATED = "WATCH INFO UPDATED";
		final String SUBSCRIPTIONRENEWED = "SUBSCRIPTION RENEWED";

		final String GPSINFONOTFOUND = "GPS INFO NOT AVAILABLE";
		final String GPSINFOFOUND = "GPS INFO FOUND";
		final String GPSINFOUPDATED = "GPS INFO UPDATED";
		final String GPSUPDATEFAILED = "GPS UPDATE FAILED";
		final String GPSINFOSAVED = "GPS INFO SAVED";
		final String GPSTRACKINGENABLED = "GPS TRACKING ENABLED";
		final String GPSTRACKINGDISABLED = "GPS TRACKING DISABLED";
		final String GPSENABLED = "PATIENT GPS ENABLED";
		final String GPSDISABLED = "PATIENT GPS DISABLED";
		final String BULKWATCHADDED = "DEVICES ADDED IN BULK SUCCESSFULLY";
		final String INVENTORYNOTFOUND = "INVENTORY ITEM NOT FOUND";
		final String INCORRECTDATEFORMAT = "INCORRECT DATE FORMAT";
		final String PATIENTSAVED = "PATIENT SAVED SUCCESSFULLY";
		final String WATCHINVENTORYTYPECHANGED = "WATCH INVENTORY TYPE CHANGED SUCCESSFULLY";
		final String WATCHINVENTORYTYPECHANGEFAILED = "WATCH INVENTORY TYPE CHANGED FAILED. CHECK XML FILE FORMAT";
		final String VISIT_VERIFICATION_SUCCESS = "PATIENT VISIT VERIFICATION SUCCESS";
		final String VISIT_VERIFICATION_FAILED = "PATIENT VISIT VERIFICATION FAILED";
		final String CUSTOMALERTS = "Custom Alerts added successfully";
		final String SCHEDULE_MESSAGE = "Schedule Message Added successfully";

		final String DISABLEDDISPLAYREMINDER = "Disabled Display Reminder";
		final String ENABLEDDISPLAYREMINDER = "Enabled Display Reminder";
		final String UPDATENOTIFICATIONFAILED = "Unable to update Notification Configuration";
		final String FAXSUCCESS = "FAX SEND SUCCESSFULLY";
	}

	interface HeartBeat {
		final int MAX_MISSED_HEARTBEAT = 2;
	}

	interface PlatformType {
		final String PLATFORMTYPE_ANDROID = "ANDROID";
		final String PLATFORMTYPE_IOS = "IOS";
		final String PLATFORMTYPE_WATCH_IOS = "WATCHOS";
	}

	interface VitalTypes {
		final String BLOOD_PRESSURE = "Blood Pressure";
		final String BLOOD_SUGAR = "Blood Sugar";
		final String SYSTOLIC_BLOOD_PRESSURE = "Systolic Blood Pressure";
		final String DIASTOLIC_BLOOD_PRESSURE = "Diastolic Blood Pressure";
		final String FASTING_BLOOD_SUGAR = "Fasting Blood Sugar";
		final String RANDOM_BLOOD_SUGAR = "Random Blood Sugar";
		final String WEIGHT = "Weight";
		final String TEMPERATURE = "Temperature";
		final String HEARTRATE = "Heart Rate";
		final String SPO2 = "Oxygen Saturation";
		final String PEDOMETER = "Pedometer";
		final String SLEEP_MONITOR = "Sleep Monitor";
	}

	interface VitalConfigurations {
		final String MANUAL = "Manual";
		final String ONDEMAND = "On Demand";
	}

	interface Validation {
		final String MANUAL = "M";
		final String AUTO = "A";
	}

	interface VitalErrorRange {
		final Double[] SYSTOLIC_BLOOD_PRESSURE = { 60.0, 255.0 };
		final Double[] DIASTOLIC_BLOOD_PRESSURE = { 30.0, 255.0 };
		final Double[] FASTING_BLOOD_SUGAR = { 20.0, 600.0 };
		final Double[] RANDOM_BLOOD_SUGAR = { 20.0, 600.0 };
		final Double[] WEIGHT = { 5.0, 250.0 };
		final Double[] TEMPERATURE = { 68.0, 113.0 };
		final Double[] HEARTRATE = { 20.0, 150.0 };
		final Double[] SPO2 = { 0.0, 100.0 };
	}
}
