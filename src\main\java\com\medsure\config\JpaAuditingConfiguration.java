package com.medsure.config;

import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.apache.log4j.Logger;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.domain.AuditorAware;
import org.springframework.data.jpa.repository.config.EnableJpaAuditing;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.RequestContextListener;
import org.springframework.web.context.request.ServletRequestAttributes;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medsure.ui.entity.server.UserVO;
import com.medsure.ui.service.server.AuthenticationController;
import com.medsure.util.AESUtil;

@Configuration
@EnableJpaAuditing(auditorAwareRef = "auditorProvider")
public class JpaAuditingConfiguration {
	private static Logger logger = Logger.getLogger(AuthenticationController.class);

	@Bean
	public RequestContextListener requestContextListener() {
		return new RequestContextListener();
	}

	@Bean
	public AuditorAware<String> auditorProvider() {
		return () -> getCurrentAuthentication();
	}

	private Optional<String> getCurrentAuthentication() {
		try {
			ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder
					.getRequestAttributes();
			HttpServletRequest request = requestAttributes.getRequest();
			UserVO user = new ObjectMapper().readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"),UserVO.class);
			logger.info("******" + user.getUserName());
			return Optional.of(user.getUserName() == null ? "SYSTEM" : user.getUserName());
		} catch (Exception e) {
			return Optional.of("SYSTEM");
		}
	}

}
