
package com.medsure.config;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.medsure.factory.WatchRxFactory;
import com.medsure.model.WatchRxPatientTextMessages;
import com.medsure.test.caregiver.CareGiverTest;
import com.medsure.ui.entity.caregiver.response.PatientInfo;

public class ScheduledForMessage {

	public static final Logger logger = LoggerFactory.getLogger(ScheduledForMessage.class);
	private static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd h:mm a");

//	@Scheduled(cron = "0 0/15 * * * *")
	public void scheduleTaskWithCronExpression() {

		logger.info("Schedule Message Sending Task Running --->" + dateTimeFormatter.format(LocalDateTime.now()));

		String vmName = ManagementFactory.getRuntimeMXBean().getName();
		int p = vmName.indexOf("@");
		String pid = vmName.substring(0, p);
		logger.info("-------> Java Application Proccess Id " + pid);

		List<WatchRxPatientTextMessages> notSentMessages = WatchRxFactory.getPatientService().getNotSentMessages();
		if (notSentMessages.size() > 0) {
			CareGiverTest sdetails = new CareGiverTest();

			for (WatchRxPatientTextMessages messageVO : notSentMessages) {
				if (!messageVO.getQuestion_time().isEmpty()) {
					LocalDateTime currentDt = LocalDateTime.now();
					LocalDateTime compareDate = LocalDateTime.parse(messageVO.getQuestion_time(), dateTimeFormatter);
					logger.info("CurrentDateTime :" + currentDt + " \nCompareDateTime:" + compareDate);
					if (compareDate.isBefore(currentDt) || compareDate.isEqual(currentDt)) {
						try {
							PatientInfo gcmId = WatchRxFactory.getPatientService()
									.getPatientById(messageVO.getWatchrxPatient().getPatientId());
							if (gcmId.getGcmId() != null) {
								JsonObject jo = new JsonObject();
								jo.addProperty("messageType", "textMessage");
								jo.addProperty("caregiverName", messageVO.getSenderName());
								jo.addProperty("question", messageVO.getQuestion());
								jo.addProperty("answer", messageVO.getAnswer());
								jo.addProperty("questionId", messageVO.getQuestionId());
								jo.addProperty("javaProccessId", pid);
								Gson gson = new Gson();
								String jsonStr = gson.toJson(jo);

								logger.info("FCM message to be sent is " + jsonStr);
								sdetails.sendMessageToPatient("message", jsonStr, gcmId.getGcmId(), gcmId.getPlatform());

								WatchRxFactory.getPatientService().updateTexMessage(messageVO.getQuestionId(),
										"waiting for response");
							}
						} catch (IOException e) {
							logger.info("Something went wrong in careGiverStatus method " + e.getMessage());
							e.printStackTrace();
						}
					}
				}
			}
		}

	}

	// Every Month 1st day ,at 6 AM SERVER TIME
	@Scheduled(cron = "0 0 6 1 * ?")
	public void checkScreeningData() {
		logger.error("Begin : Screening Scheduler is running......");
		WatchRxFactory.getPatientService().runScreening();
		logger.error("End : Screening Scheduler is running......");
	}

	// Every Month 1st day ,at 12 AM SERVER TIME
	@Scheduled(cron = "0 0 0 1 * ?")
	public void resetBillingStatus() {
		logger.error("Begin : Billing Status Reset......");
		WatchRxFactory.getPatientService().resetStatusMonthBegining();
		logger.error("End : Billing Status Reset......");
	}
}
