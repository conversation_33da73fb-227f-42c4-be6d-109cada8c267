package com.medsure.dao.impl;

import java.util.Arrays;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.common.Constants.VitalTypes;
import com.medsure.dao.ThresholdConfigurationDAO;
import com.medsure.model.WatchRxPatientThresholdConfiguration;

@Component
public class ThresholdConfigurationDAOImpl extends BaseDAOImpl<WatchRxPatientThresholdConfiguration>
		implements ThresholdConfigurationDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientThresholdConfiguration> getThresholdConfigurationForPatientVitalType(Long patientId,
			String vitalTypeName) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q;
		List<WatchRxPatientThresholdConfiguration> result;
		try {
			q = em.createQuery(
					"SELECT e FROM WatchRxPatientThresholdConfiguration e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxVitalsType.vitalTypeName =:vitalTypeName");
			q.setParameter("patientId", patientId).setParameter("vitalTypeName", vitalTypeName);
			result = q.getResultList();
		} finally {
			em.close();
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientThresholdConfiguration> getThresholdConfigurationForPatientVitalTypeIds(Long patientId,
			List<Long> vitalTypeIdList) {

		EntityManager em = entityManagerFactory.createEntityManager();
		Query q;
		List<WatchRxPatientThresholdConfiguration> result;
		try {
			q = em.createQuery(
					"SELECT e FROM WatchRxPatientThresholdConfiguration e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxVitalsType.vitalTypeId In :vitalTypeIdList");
			q.setParameter("patientId", patientId).setParameter("vitalTypeIdList", vitalTypeIdList);
			result = q.getResultList();
		} finally {
			em.close();
		}

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientThresholdConfiguration> getStatusForVitals(Long patientId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<String> vitalTypeNameList = Arrays.asList(VitalTypes.RANDOM_BLOOD_SUGAR, VitalTypes.FASTING_BLOOD_SUGAR,
				VitalTypes.SYSTOLIC_BLOOD_PRESSURE, VitalTypes.DIASTOLIC_BLOOD_PRESSURE, VitalTypes.HEARTRATE, VitalTypes.PEDOMETER,VitalTypes.TEMPERATURE,VitalTypes.WEIGHT,VitalTypes.SPO2);
		Query q;
		List<WatchRxPatientThresholdConfiguration> result;
		try {
			q = em.createQuery(
					"SELECT e FROM WatchRxPatientThresholdConfiguration e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxVitalsType.vitalTypeName NOT In :vitalTypeNameList");
			q.setParameter("patientId", patientId).setParameter("vitalTypeNameList", vitalTypeNameList);
			result = q.getResultList();
		} finally {
			em.close();
		}

		return result;
	}
	
	
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientThresholdConfiguration> getVitalsConfig(Long patientId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<String> vitalTypeNameList = Arrays.asList(VitalTypes.RANDOM_BLOOD_SUGAR, VitalTypes.FASTING_BLOOD_SUGAR,
				VitalTypes.SYSTOLIC_BLOOD_PRESSURE, VitalTypes.DIASTOLIC_BLOOD_PRESSURE, VitalTypes.HEARTRATE, VitalTypes.PEDOMETER,VitalTypes.TEMPERATURE,VitalTypes.WEIGHT,VitalTypes.SPO2);
		Query q;
		List<WatchRxPatientThresholdConfiguration> result;
		try {
			q = em.createQuery(
					"SELECT e FROM WatchRxPatientThresholdConfiguration e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxVitalsType.vitalTypeName In :vitalTypeNameList");
			q.setParameter("patientId", patientId).setParameter("vitalTypeNameList", vitalTypeNameList);
			result = q.getResultList();
		} finally {
			em.close();
		}

		return result;
	}
	
	

}
