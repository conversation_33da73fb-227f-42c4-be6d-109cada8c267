package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.common.Constants;
import com.medsure.dao.UserDAO;
import com.medsure.model.WatchrxUser;

/**
 * <AUTHOR>
 *
 */
@Component
public class UserDAOImpl extends BaseDAOImpl<WatchrxUser> implements UserDAO {

	public WatchrxUser findByemailAndUserType(String email) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query query = em.createQuery("SELECT e FROM WatchrxUser e WHERE e.email='" + email + "' AND e.userType='"
					+ Constants.UserType.CAREGIVER + "'");
			return (WatchrxUser) query.getSingleResult();
		} finally {
			em.close();
		}
	}

	@Override
	public List<WatchrxUser> authenticateUser(String email, String otp) {
		System.out.println("In UserDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em
				.createQuery("SELECT e FROM WatchrxUser e WHERE e.email = :propertyValue1 AND e.otp = :propertyValue2");
		query.setParameter("propertyValue1", email);
		query.setParameter("propertyValue2", Integer.valueOf(otp));
		@SuppressWarnings("unchecked")
		List<WatchrxUser> list = query.getResultList();
		return list;
	}

	@Override
	public List<WatchrxUser> validateUserOTPStatus(String email, String otpStatus) {
		System.out.println("In PatientDAOImpl:findByProperty");
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery(
				"SELECT e FROM WatchrxUser e WHERE e.email = :propertyValue1 AND e.otpStatus = :propertyValue2");
		query.setParameter("propertyValue1", email);
		query.setParameter("propertyValue2", otpStatus);
		@SuppressWarnings("unchecked")
		List<WatchrxUser> list = query.getResultList();
		return list;
	}

	@Override
	public List<WatchrxUser> getAllUsers() {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxUser e WHERE e.userType IN (3,5)");
		@SuppressWarnings("unchecked")
		List<WatchrxUser> list = query.getResultList();
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxUser> getUserByNameAndRole(String userName, Integer userRole) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery(
				"SELECT e FROM WatchrxUser e WHERE e.userName = :userName AND e.userType = :userRole");
		query.setParameter("userName", userName);
		query.setParameter("userRole", userRole);
		List<WatchrxUser> list = query.getResultList();
		return list;
	}
}