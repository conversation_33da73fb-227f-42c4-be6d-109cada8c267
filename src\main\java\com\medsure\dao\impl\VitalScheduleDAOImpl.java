package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.VitalScheduleDAO;
import com.medsure.model.WatchRxPatientVital;
import com.medsure.model.WatchRxVitalSchedule;

@Component
public class VitalScheduleDAOImpl extends BaseDAOImpl<WatchRxVitalSchedule> implements VitalScheduleDAO{
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxVitalSchedule> getVitalSchedulesForPatientVitalType(Long patientId, String vitalTypeName) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxVitalSchedule> result;
		try {
			Query q = em.createQuery("SELECT e FROM WatchRxVitalSchedule e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxVitalsType.vitalTypeName =:vitalTypeName ORDER BY e.updatedDate DESC ");
			q.setParameter("patientId", patientId).setParameter("vitalTypeName", vitalTypeName);
			result = q.getResultList();
		}finally {
			em.close();
		}
		return result;
	}
	
	@Override
	@SuppressWarnings("unchecked")
	public List<WatchRxPatientVital> getVitalScheduleForId(Long patientId, Long scheduleId ) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q;
		List<WatchRxPatientVital> result;
		try{
			q = em.createQuery("SELECT e "
					+ "FROM WatchRxVitalSchedule e "
					+ "WHERE e.watchrxPatient.patientId = :patientId "
					+ "AND e.vitalScheduleId = :scheduleId ");
			q.setParameter("patientId", patientId).setParameter("scheduleId", scheduleId);
			result = q.getResultList();
		}
		finally {
			em.close();
		}
		return result;
	}

}
