package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchAPKDAO;
import com.medsure.model.WatchrxWatchAPK;

/**
 * <AUTHOR>
 *
 */
@Component
public class WatchAPKDAOImpl extends BaseDAOImpl<WatchrxWatchAPK> implements WatchAPKDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchAPK> getWatchApkByAndroidVersionApkVersion(String androidVersion, String apkVersion) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxWatchAPK e WHERE e.apkVersion='" + apkVersion
				+ "' AND e.androidVersion" + "= '" + androidVersion + "'");
		List<WatchrxWatchAPK> list = query.getResultList();
		return list;
	}

	@Override
	public List<WatchrxWatchAPK> getWatchApkByAndroidVersionApkVersionServerInstanceVersion(String androidVersion,
			String apkVersion, String instanceVersion) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxWatchAPK e WHERE e.apkVersion='" + apkVersion
				+ "' AND e.androidVersion" + "= '" + androidVersion + "' AND e.serverInstance='"+instanceVersion+"'");
		@SuppressWarnings("unchecked")
		List<WatchrxWatchAPK> list = query.getResultList();
		return list;
	}

}