package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchCardDAO;
import com.medsure.model.WatchrxCard;

@Component
public class WatchCardDAOImpl extends BaseDAOImpl<WatchrxCard> implements WatchCardDAO {

	@Override
	@SuppressWarnings("unchecked")
	public WatchrxCard findDefaultCardByClinicianId(Long clinicianId) {
		// TODO Auto-generated method stub
		
		
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxCard e WHERE e.isDefault = TRUE AND e.clinician.clinicianId = :propertyValue");
    	query.setParameter("propertyValue", clinicianId);
    	List<WatchrxCard> list = query.getResultList();	
    	
    	if(list.size()>0)
    		return list.get(0);
    	else 
    		return null;
	}

}
