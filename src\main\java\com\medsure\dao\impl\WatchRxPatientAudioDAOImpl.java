package com.medsure.dao.impl;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientAudioDAO;
import com.medsure.model.WatchrxAudio;

@Component
public class WatchRxPatientAudioDAOImpl extends BaseDAOImpl<WatchrxAudio> implements WatchRxPatientAudioDAO{
	
	@Override
	public WatchrxAudio getAudioDataByName(String name) {

		EntityManager em = entityManagerFactory.createEntityManager();

		WatchrxAudio audio = (WatchrxAudio) em.createQuery(
				"SELECT a FROM WatchrxAudio a where a.audioFilePath LIKE CONCAT(:name,'%')")
				.setParameter("name", name).getSingleResult();
		return audio;
	}

}
