package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientCarePlanQandAnsDAO;
import com.medsure.model.WatchRxPatientCarePlanQandAns;

@Component
public class WatchRxPatientCarePlanQandAnsDAOImpl extends BaseDAOImpl<WatchRxPatientCarePlanQandAns>
		implements WatchRxPatientCarePlanQandAnsDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientCarePlanQandAns> getCarePlanDataByPatientId(Long patientId, Long carePlanId) {
		List<WatchRxPatientCarePlanQandAns> result = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			result = em.createQuery(
					"SELECT a FROM WatchRxPatientCarePlanQandAns a WHERE a.watchrxPatient.patientId = :patientId AND a.watchRxCarePlanQuestions.id =:carePlanId"
							+ "  ORDER BY a.createdDate DESC")
					.setParameter("patientId", patientId).setParameter("carePlanId", carePlanId).getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientCarePlanQandAns> getCarePlanDetailsByPatientId(Long patientId, Date startDate,
			Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q;
		List<WatchRxPatientCarePlanQandAns> result;
		try {
			q = em.createQuery(
					"SELECT e FROM WatchRxPatientCarePlanQandAns e WHERE e.watchrxPatient.patientId = :patientId "
							+ " AND (e.createdDate BETWEEN :startDate AND :endDate) ORDER BY e.watchRxCarePlanQuestions.id ASC , e.createdDate DESC ");
			q.setParameter("patientId", patientId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endDate);
			result = q.getResultList();
		} finally {
			em.close();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientCarePlanQandAns> getCarePlanHistoryByPatientIdAndDate(Long patientId, Long carePlanId,
			Date startDate, Date endDate) {

		System.out.println("Getting history Data: patientId: " + patientId + " carePlanId:" + carePlanId
				+ " startDate: " + startDate + " endDate: " + endDate);

		EntityManager em = entityManagerFactory.createEntityManager();
		Query q;
		List<WatchRxPatientCarePlanQandAns> result;
		try {
			q = em.createQuery(
					"SELECT e FROM WatchRxPatientCarePlanQandAns e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxCarePlanQuestions.id =:carePlanId "
							+ " AND (e.createdDate BETWEEN :startDate AND :endDate) ORDER BY e.createdDate DESC ");
			q.setParameter("patientId", patientId);
			q.setParameter("carePlanId", carePlanId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endDate);
			result = q.getResultList();
		} finally {
			em.close();
		}
		return result;
	}
}
