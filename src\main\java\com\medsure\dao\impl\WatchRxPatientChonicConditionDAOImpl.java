package com.medsure.dao.impl;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientChronicConditionDAO;
import com.medsure.model.WatchRxPatientChronicConditions;

@Component
public class WatchRxPatientChonicConditionDAOImpl extends BaseDAOImpl<WatchRxPatientChronicConditions>
		implements WatchRxPatientChronicConditionDAO {

	@Override
	public Long ccmPatientCount(Long orgId, Long clinicianId, Integer role) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long count = null;
		try {
			if (role == 3) {
				Query q = em.createQuery(
						"SELECT count(DISTINCT e.watchrxPatient.patientId) FROM WatchRxPatientChronicConditions e INNER JOIN WatchrxPatient p "
								+ " ON p.patientId = e.watchrxPatient.patientId "
								+ " AND p.watchrxGroup.groupId =:orgId WHERE "
								+ " (SELECT COUNT(c) FROM WatchRxPatientChronicConditions c WHERE c.watchrxPatient.patientId = e.watchrxPatient.patientId) >= 2"); 
				q.setParameter("orgId", orgId);
				count = (Long) q.getSingleResult();
			} else {
				Query q = em.createQuery(
						"SELECT count(DISTINCT e.watchrxPatient.patientId) FROM WatchRxPatientChronicConditions e INNER JOIN WatchrxPatient p "
								+ " ON p.patientId = e.watchrxPatient.patientId AND e.watchrxPatient.patientId IN "
								+ " (SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b "
								+ "  WHERE b.watchrxPatient.status='Y' AND b.watchrxClinician.clinicianId = :clinicianId)"
								+ " AND p.watchrxGroup.groupId =:orgId WHERE "
								+ " (SELECT COUNT(c) FROM WatchRxPatientChronicConditions c WHERE c.watchrxPatient.patientId = e.watchrxPatient.patientId) >= 2");
				q.setParameter("clinicianId", clinicianId);
				q.setParameter("orgId", orgId);
				count = (Long) q.getSingleResult();
			}
		} catch (Exception e) {

		} finally {
			em.close();
		}
		return count;
	}

	@Override
	public Long pcmPatientCount(Long orgId, Long clinicianId, Integer role) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long count = null;
		try {
			if (role == 3) {
				Query q = em.createQuery(
						"SELECT count(DISTINCT e.watchrxPatient.patientId) FROM WatchRxPatientChronicConditions e INNER JOIN WatchrxPatient p "
								+ " ON p.patientId = e.watchrxPatient.patientId"
								+ " AND p.watchrxGroup.groupId =:orgId WHERE "
								+ " (SELECT COUNT(c) FROM WatchRxPatientChronicConditions c WHERE c.watchrxPatient.patientId = e.watchrxPatient.patientId) = 1"); 
				q.setParameter("orgId", orgId);
				count = (Long) q.getSingleResult();
			} else {
				Query q = em.createQuery(
						"SELECT count(DISTINCT e.watchrxPatient.patientId) FROM WatchRxPatientChronicConditions e INNER JOIN WatchrxPatient p "
								+ " ON p.patientId = e.watchrxPatient.patientId AND e.watchrxPatient.patientId IN "
								+ " (SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b "
								+ "  WHERE b.watchrxPatient.status='Y' AND b.watchrxClinician.clinicianId = :clinicianId)"
								+ " AND p.watchrxGroup.groupId =:orgId WHERE "
								+ " (SELECT COUNT(c) FROM WatchRxPatientChronicConditions c WHERE c.watchrxPatient.patientId = e.watchrxPatient.patientId) = 1");
				q.setParameter("clinicianId", clinicianId);
				q.setParameter("orgId", orgId);
				count = (Long) q.getSingleResult();
			}
		} catch (Exception e) {

		} finally {
			em.close();
		}
		return count;
	}
}
