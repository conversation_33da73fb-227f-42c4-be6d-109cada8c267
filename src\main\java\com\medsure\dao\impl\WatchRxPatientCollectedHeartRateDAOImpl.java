package com.medsure.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientCollectedHeartRateDAO;
import com.medsure.model.WatchRxPatientCollectedHeartRates;

@Component
public class WatchRxPatientCollectedHeartRateDAOImpl extends BaseDAOImpl<WatchRxPatientCollectedHeartRates>
		implements WatchRxPatientCollectedHeartRateDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientCollectedHeartRates> getHeartRatesByPatientId(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientCollectedHeartRates> data = null;

		try {
			data = em
					.createQuery("SELECT e FROM WatchRxPatientCollectedHeartRates e WHERE  e.watchrxPatient.patientId ="
							+ patientId + "  ORDER BY  e.createdDate DESC")
					.getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientCollectedHeartRates> getHeartRatesByPatientIdPaginated(Long patientId, Integer index, Integer pageSize) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientCollectedHeartRates> data = null;

		try {
			data = em
					.createQuery("SELECT e FROM WatchRxPatientCollectedHeartRates e WHERE  e.watchrxPatient.patientId ="
							+ patientId + "  ORDER BY  e.createdDate DESC").setFirstResult(index * pageSize).setMaxResults(pageSize)
					.getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}
	
	@Override
	public Long getHeartRatesByPatientIdResultCount(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Long result = null;

		try {
			result = (Long) em.createQuery("SELECT COUNT(e) FROM WatchRxPatientCollectedHeartRates e WHERE  e.watchrxPatient.patientId ="
							+ patientId )
					.getSingleResult();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return result;
	}
	
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> collectedHeartRateCountMonth(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.createdDate),AVG(a.heartRate) FROM WatchRxPatientCollectedHeartRates a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.createdDate BETWEEN :startDate AND :endDate "
						+ "GROUP BY DAYOFMONTH(a.createdDate)")
				.setParameter("startDate", startDate).setParameter("endDate", endDate);
		q.setParameter("patientId", patientId);
		List<Object[]> result = q.getResultList();

		return result;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> collectedHeartRateCountYear(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.createdDate), AVG(a.heartRate) FROM WatchRxPatientCollectedHeartRates a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.createdDate BETWEEN :startDate AND :endDate " + "GROUP BY MONTH(a.createdDate)")
				.setParameter("startDate", startDate).setParameter("endDate", endDate);
		q.setParameter("patientId", patientId);
		List<Object[]> result = q.getResultList();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientCollectedHeartRates> collectedHeartRateTimeIntervalPatient(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT a FROM WatchRxPatientCollectedHeartRates a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.createdDate BETWEEN :startDate AND :endDate ");
		q.setParameter("patientId", patientId).setParameter("startDate", startDate).setParameter("endDate", endDate);
		List<WatchRxPatientCollectedHeartRates> result = q.getResultList();

		return result;
	}
	
	

}
