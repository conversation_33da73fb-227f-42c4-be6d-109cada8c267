package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientDiyvaDialogDAO;
import com.medsure.model.WatchRxPatientDiyvaDialogs;

@Component
public class WatchRxPatientDiyvaDialogDAOImpl extends BaseDAOImpl<WatchRxPatientDiyvaDialogs> implements WatchRxPatientDiyvaDialogDAO{

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientDiyvaDialogs> getDlgandPat(Long patId, Long dlgId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchRxPatientDiyvaDialogs e WHERE e.watchrxPatient.patientId=:patId"
				+ " AND e.dialogs.dialogId=:dlgId").setParameter("patId", patId).setParameter("dlgId", dlgId);
		List<WatchRxPatientDiyvaDialogs> list = query.getResultList();
		System.out.println(list.size()
				);
		return list;
	}
}

