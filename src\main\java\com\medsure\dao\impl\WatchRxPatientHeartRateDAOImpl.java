package com.medsure.dao.impl;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientHeartRateDAO;
import com.medsure.model.WatchRxPatientHeartRate;

@Component
public class WatchRxPatientHeartRateDAOImpl extends BaseDAOImpl<WatchRxPatientHeartRate>
		implements WatchRxPatientHeartRateDAO {

	@Override
	public WatchRxPatientHeartRate getHeartRateSettingBypId(Long pId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		WatchRxPatientHeartRate data = null;

		try {
			data = (WatchRxPatientHeartRate) em
					.createQuery("SELECT e FROM WatchRxPatientHeartRate e WHERE  e.watchrxPatient.patientId =" + pId)
					.getSingleResult();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;

	}

}
