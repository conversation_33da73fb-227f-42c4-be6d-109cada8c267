package com.medsure.dao.impl;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientRecentQuestionsDAO;
import com.medsure.model.WatchRxPatientRecentQuestions;

@Component
public class WatchRxPatientRecentQuestionsDAOImpl extends BaseDAOImpl<WatchRxPatientRecentQuestions>
		implements WatchRxPatientRecentQuestionsDAO {

	@Override
	public void updateRecentlyAskedQuestions(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			Query query = em.createQuery(
					"UPDATE WatchRxPatientRecentQuestions e set e.isAsked = false where e.watchrxPatient.patientId = :patientId ");
			query.setParameter("patientId", patientId);
			query.executeUpdate();
			em.getTransaction().commit();
		} finally {
			em.close();
		}
	}
}
