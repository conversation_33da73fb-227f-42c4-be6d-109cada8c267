package com.medsure.dao.impl;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.medsure.dao.WatchRxPatientRpmCcmDataDetailsDAO;
import com.medsure.model.WatchRxPatientRpmCcmDataDetails;
import com.medsure.ui.entity.server.adminreport.CPTCodeReport;

@Component
public class WatchRxPatientRpmCcmDataDetailsDAOImpl extends BaseDAOImpl<WatchRxPatientRpmCcmDataDetails>
		implements WatchRxPatientRpmCcmDataDetailsDAO {

	public final String GET_PATIENTS_BY_ORGID_AND_CLINICIANID = "SELECT DISTINCT p.PATIENT_ID, CONCAT(p.FIRST_NAME, \" \", p.LAST_NAME) PatientName, "
			+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, CONCAT(c.FIRST_NAME, \" \", c.LAST_NAME) CaregiverName, rpc.RPM_MINS, rpc.CCM_MINS,	"
			+ "rpc.PCM_MINS,rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT p "
			+ "JOIN WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc ON rpc.FK_PATIENT_ID = p.PATIENT_ID AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) "
			+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
			+ "LEFT JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId ";

	@Override
	public WatchRxPatientRpmCcmDataDetails isPatientRpmCcmFound(Long patientId) {
		WatchRxPatientRpmCcmDataDetails rpmCcmDataDetails = null;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchRxPatientRpmCcmDataDetails a WHERE a.watchrxPatient.patientId = :patientId AND "
							+ " YEAR(a.updatedDate) = YEAR(CURDATE()) AND MONTH(a.updatedDate) = MONTH(CURDATE())");
			q.setParameter("patientId", patientId);
			rpmCcmDataDetails = (WatchRxPatientRpmCcmDataDetails) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return rpmCcmDataDetails;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getRpmCcmPcmPatientsCount(Long clinicianId, Long orgId, Integer role) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (role == 3) {
				patientsList = em
						.createNativeQuery("SELECT "
								+ "  SUM(CASE WHEN RPM_MINS < 20 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
								+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
								+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS < 20 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count,"
								+ "  SUM(CASE WHEN PCM_MINS < 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
								+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count,"
								+ "  SUM(CASE WHEN DAYS_MEASURED < 16 THEN 1 ELSE 0 END) AS days_measured_count"
								+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND "
								+ "  MONTH(rpc.UPADATED_DATE) = MONTH(CURRENT_DATE()) "
								+ "  AND YEAR(rpc.UPADATED_DATE) = YEAR(CURRENT_DATE());")
						.setParameter("orgId", orgId).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS < 20 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS < 20 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS < 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count,"
						+ "  SUM(CASE WHEN DAYS_MEASURED < 16 THEN 1 ELSE 0 END) AS days_measured_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND "
						+ "  MONTH(rpc.UPADATED_DATE) = MONTH(CURRENT_DATE()) "
						+ "  AND YEAR(rpc.UPADATED_DATE) = YEAR(CURRENT_DATE());").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@Override
	public BigInteger dataCollection16DaysPatientCount(Long clinicianId, Long orgId, Integer role) {
		Object num = 0;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				num = em.createNativeQuery(
						"SELECT COUNT(rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "WHERE p.GROUP_ID = :orgId AND MONTH(rpc.UPADATED_DATE) = MONTH(CURRENT_DATE()) "
								+ "AND YEAR(rpc.UPADATED_DATE) = YEAR(CURRENT_DATE()) AND DAYS_MEASURED >= 16")
						.setParameter("orgId", orgId).getSingleResult();
			} else {
				num = em.createNativeQuery(
						"SELECT COUNT(rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "WHERE p.GROUP_ID = :orgId AND pc.FK_CLINICIAN_ID = :clinicianId "
								+ "AND MONTH(rpc.UPADATED_DATE) = MONTH(CURRENT_DATE()) "
								+ "AND YEAR(rpc.UPADATED_DATE) = YEAR(CURRENT_DATE()) AND DAYS_MEASURED >= 16")
						.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId).getSingleResult();
			}
			if (num instanceof BigInteger) {
				return (BigInteger) num;
			} else if (num instanceof Number) {
				return BigInteger.valueOf(((Number) num).longValue());
			} else {
				throw new ClassCastException("Unexpected result type: " + num.getClass().getName());
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return BigInteger.ZERO;
	}

	@Override
	public Long getAllPatientsCountByOrgIdAndClinicianIdAndFilterType(Long orgId, Long clinicianId, Integer role,
			String filterType, Date startDate, Date endDate) {
		Long count = 0l;
		Object patientCounts = 0l;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				if (filterType.equalsIgnoreCase("all") || filterType.equalsIgnoreCase("sort")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm20")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm40")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 21 AND 39 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm60")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm20")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm40")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 21 AND 39 "
									+ "AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm60")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm30")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS < 30 "
									+ "AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm60")) {
					patientCounts = em.createNativeQuery(
							"SELECT COUNT(*) FROM ( SELECT DISTINCT p.PATIENT_ID, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS BETWEEN 30 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)) AS SUBQUERY")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("daysMeasure")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED < 16 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				}
			} else {
				if (filterType.equalsIgnoreCase("all") || filterType.equalsIgnoreCase("sort")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm20")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm40")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS BETWEEN 21 AND 39 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm60")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm20")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm40")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS BETWEEN 21 AND 39 "
									+ "AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm60")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm30")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.PCM_MINS < 30 "
									+ "AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm60")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.PCM_MINS BETWEEN 30 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("daysMeasure")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
//									+ " JOIN WATCHRX_PATIENT_WATCH_ASSIGNMNT pw ON pw.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED < 16 AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			return 0l;
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsByOrgIdAndClinicianIdAndFilterType(Long orgId, Long clinicianId, Integer role,
			String filterType, Integer index, Integer pageSize, Date startDate, Date endDate, String sortCol,
			String sortDir) {
		List<Object[]> patients = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				if (filterType.equalsIgnoreCase("sort")) {
					String queryString = "SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
							+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, "
							+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS"
							+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
							+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
							+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
							+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
							+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)";

					// Add sorting if parameters are provided
					if (sortCol != null && !sortCol.isEmpty()) {
						String clName = "rpc.RPM_MINS";
						if (sortCol.equalsIgnoreCase("rpm")) {
							clName = "rpc.RPM_MINS";
						}
						if (sortCol.equalsIgnoreCase("ccm")) {
							clName = "rpc.CCM_MINS";
						}
						if (sortCol.equalsIgnoreCase("pcm")) {
							clName = "rpc.PCM_MINS";
						}
						if (sortCol.equalsIgnoreCase("dataCollection")) {
							clName = "rpc.DAYS_MEASURED";
						}
						if (sortCol.equalsIgnoreCase("chart")) {
							clName = "rpc.CM_REV_MINS";
						}
						if (sortCol.equalsIgnoreCase("status")) {
							clName = " CASE " + " WHEN (rpc.RPM_MINS >= 20 OR rpc.CCM_MINS >= 20 OR rpc.PCM_MINS >= 30 "
									+ " OR rpc.DAYS_MEASURED >= 16 )THEN 1 " + " ELSE 0 " + " END";
						}
						queryString += " ORDER BY " + clName;
						if (sortDir != null && !sortDir.isEmpty()) {
							queryString += " " + sortDir;
						} else {
							queryString += " DESC"; // default direction
						}
						if (sortCol.equalsIgnoreCase("status")) {
							queryString += " ,rpc.PCM_MINS DESC";
						}
					}
					patients = em.createNativeQuery(queryString).setFirstResult(index * pageSize)
							.setMaxResults(pageSize).setParameter("orgId", orgId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("all")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS"
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.PCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm20")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS < 20 "
									+ " AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.RPM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm40")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 20 AND 39 "
									+ " AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.RPM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm60")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.RPM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm20")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS < 20 "
									+ " AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.CCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm40")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 20 AND 39 "
									+ " AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.CCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm60")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.CCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm30")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS < 30 "
									+ " AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.PCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm60")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS BETWEEN 30 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.PCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("daysMeasure")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED < 16"
									+ "  AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
				}
			} else {
				if (filterType.equalsIgnoreCase("sort")) {

					String queryString = GET_PATIENTS_BY_ORGID_AND_CLINICIANID;

					if (sortCol != null && !sortCol.isEmpty()) {
						String clName = "rpc.RPM_MINS";
						if (sortCol.equalsIgnoreCase("rpm")) {
							clName = "rpc.RPM_MINS";
						}
						if (sortCol.equalsIgnoreCase("ccm")) {
							clName = "rpc.CCM_MINS";
						}
						if (sortCol.equalsIgnoreCase("pcm")) {
							clName = "rpc.PCM_MINS";
						}
						if (sortCol.equalsIgnoreCase("dataCollection")) {
							clName = "rpc.DAYS_MEASURED";
						}
						if (sortCol.equalsIgnoreCase("chart")) {
							clName = "rpc.CM_REV_MINS";
						}
						if (sortCol.equalsIgnoreCase("status")) {
							clName = " CASE " + " WHEN (rpc.RPM_MINS >= 20 OR rpc.CCM_MINS >= 20 OR rpc.PCM_MINS >= 30 "
									+ " OR rpc.DAYS_MEASURED >= 16 )THEN 1 " + " ELSE 0 " + " END";
						}
						queryString += " ORDER BY " + clName;
						if (sortDir != null && !sortDir.isEmpty()) {
							queryString += " " + sortDir;
						}
						if (sortCol.equalsIgnoreCase("status")) {
							queryString += " ,rpc.PCM_MINS DESC";
						}
					}
					System.out.println(" Final Query: " + queryString);
					patients = em.createNativeQuery(queryString).setFirstResult(index * pageSize)
							.setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("all")) {
					patients = em
							.createNativeQuery(GET_PATIENTS_BY_ORGID_AND_CLINICIANID + " ORDER BY rpc.PCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm20")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS < 20 "
									+ " AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.RPM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm40")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS BETWEEN 20 AND 39 "
									+ " AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.RPM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm60")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.RPM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm20")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS < 20 "
									+ " AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.CCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm40")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS BETWEEN 20 AND 39 "
									+ " AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.CCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm60")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.CCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm30")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.PCM_MINS < 30 "
									+ " AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.PCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm60")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.PCM_MINS BETWEEN 30 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.PCM_MINS DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("daysMeasure")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
									+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
//									+ " JOIN WATCHRX_PATIENT_WATCH_ASSIGNMNT pw ON pw.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED < 16 AND  pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
									+ " AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.DAYS_MEASURED DESC")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@Override
	public Long getAllPatientsCountByOrgIdAndClinicianIdAndFilterTypeSearch(Long orgId, Long clinicianId, Integer role,
			String search, Date startDate, Date endDate) {
		Long count = 0l;
		Object patientCounts = 0l;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				patientCounts = em.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND "
						+ " (p.FIRST_NAME LIKE CONCAT('%',:name,'%') OR p.LAST_NAME LIKE CONCAT('%',:name,'%') OR p.mrn LIKE CONCAT('%',:name,'%')) "
						+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("name", search).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getSingleResult();
				count = ((BigInteger) patientCounts).longValue();
			} else {
				patientCounts = em.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND "
						+ " (p.FIRST_NAME  LIKE CONCAT('%',:name,'%') OR p.LAST_NAME LIKE CONCAT('%',:name,'%') OR p.mrn LIKE CONCAT('%',:name,'%')) "
						+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("name", search).setParameter("clinicianId", clinicianId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
				count = ((BigInteger) patientCounts).longValue();
			}
		} catch (Exception e) {
			e.printStackTrace();
			return 0l;
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsByOrgIdAndClinicianIdAndFilterTypeSearch(Long orgId, Long clinicianId,
			Integer role, String search, Date startDate, Date endDate) {

		List<Object[]> patients = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				patients = em.createNativeQuery(
						"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName , "
								+ " p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER,"
								+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED , rpc.CM_REV_MINS "
								+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND "
								+ " (p.FIRST_NAME LIKE CONCAT('%',:name,'%') OR p.LAST_NAME LIKE CONCAT('%',:name,'%') OR p.MRN LIKE CONCAT('%',:name,'%')) "
								+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
						.setParameter("orgId", orgId).setParameter("name", search).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			} else {
				patients = em.createNativeQuery(
						"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName , "
								+ " p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
								+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED, rpc.CM_REV_MINS "
								+ " FROM WATCHRX_PATIENT p "
								+ " LEFT JOIN WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "	AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) "
								+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " LEFT JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
								+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND "
								+ " (p.FIRST_NAME LIKE CONCAT('%',:name,'%') OR p.LAST_NAME LIKE CONCAT('%',:name,'%') OR p.MRN LIKE CONCAT('%',:name,'%')) ")
						.setParameter("orgId", orgId).setParameter("name", search)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsRpmCcmPcmCount(Long orgId, Date startDate, Date endDate, Long careManagerId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (careManagerId != null && careManagerId > 0) {
				patientsList = em.createNativeQuery(
						"SELECT SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
								+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
								+ "  SUM(CASE WHEN RPM_MINS > 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS > 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count,"
								+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
								+ "  SUM(CASE WHEN PCM_MINS > 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count,"
								+ "  SUM(CASE WHEN DAYS_MEASURED >= 16 THEN 1 ELSE 0 END) AS days_measured_count"
								+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "  LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
								+ "  LEFT OUTER JOIN WATCHRX_USER u ON u.USER_ID = c.FK_USER_ID "
								+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND u.USER_ID = :userId AND "
								+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setParameter("orgId", orgId).setParameter("startDate", startDate)
						.setParameter("userId", careManagerId).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery(
						"SELECT SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
								+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
								+ "  SUM(CASE WHEN RPM_MINS > 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
								+ "  SUM(CASE WHEN CCM_MINS > 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count,"
								+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
								+ "  SUM(CASE WHEN PCM_MINS > 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count,"
								+ "  SUM(CASE WHEN DAYS_MEASURED >= 16 THEN 1 ELSE 0 END) AS days_measured_count"
								+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND "
								+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setParameter("orgId", orgId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsByOrgIdForBillingStats(Long orgId, Integer index, Integer pageSize,
			Date startDate, Date endDate, Long careManagerId, Long roleType) {
		List<Object[]> patients = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			System.out.println("Stats Report : " + orgId + " " + careManagerId);
			if (careManagerId != null && careManagerId > 0 && roleType == 5) {
				patients = em.createNativeQuery(
						"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
								+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
								+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
								+ " LEFT OUTER JOIN WATCHRX_USER u ON u.USER_ID = c.FK_USER_ID "
								+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND u.USER_ID = :userId AND  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
						.setParameter("userId", careManagerId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			} else {
				patients = em.createNativeQuery(
						"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
								+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
								+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getAllPatientsByOrgIdForBillingReport(Long orgId, Date startDate, Date endDate) {
		List<Object[]> patients = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			patients = em.createNativeQuery(
					"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
							+ " p.PHONE_NUMBER, p.DOB, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED , "
							+ " concat(py.FIRST_NAME,\" \",py.LAST_NAME) Physician_Name, rpc.IS_DEVICE_ASSIGNED , concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName"
							+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
							+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
							+ " LEFT OUTER JOIN WATCHRX_PHYSCIAN py ON (p.FK_PHYSICIAN_ID = py.PHYSCIAN_ID)"
							+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
							+ " LEFT JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
							+ " WHERE p.GROUP_ID = :orgId AND ( p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D')  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
					.setParameter("orgId", orgId).setParameter("startDate", startDate).setParameter("endDate", endDate)
					.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@Override
	public Long latestCalledPatients(Long orgId, Long userId, Integer role, Date startDate, Date endDate) {
		Long count = 0l;
		Object patientCounts = 0l;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				patientCounts = em.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setParameter("orgId", orgId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getSingleResult();
				count = ((BigInteger) patientCounts).longValue();
			} else {
				patientCounts = em.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setParameter("orgId", orgId).setParameter("clinicianId", userId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
				count = ((BigInteger) patientCounts).longValue();
			}
		} catch (Exception e) {
			e.printStackTrace();
			return 0l;
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> latestCalledPatients(Long orgId, Long userId, Integer role, Integer index, Integer pageSize,
			Date startDate, Date endDate) {
		List<Object[]> patients = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				patients = em.createNativeQuery(
						"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
								+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\\\" \\\", c.LAST_NAME) CaregiverName, "
								+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED , rpc.UPADATED_DATE"
								+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " LEFT JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
								+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
								+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ORDER BY rpc.UPADATED_DATE DESC")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patients = em.createNativeQuery(
						"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
								+ "p.DOB, p.PRIMARY_CASE_MANAGER_ID, p.PHONE_NUMBER, concat(c.FIRST_NAME,\" \", c.LAST_NAME) CaregiverName, "
								+ " rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED , rpc.UPADATED_DATE "
								+ " FROM WATCHRX_PATIENT p "
								+ " JOIN WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "	AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) "
								+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " LEFT JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
								+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId ")
						.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate)
						.setParameter("clinicianId", userId).getResultList();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getRpmPatientsCount(Long clinicianId, Long orgId, Integer role, Date startDate,
			Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (role == 3) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS < 20 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS > 59 THEN 1 ELSE 0 END) AS rpm_60_mins_plus "
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 AND "
						+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS < 20 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS > 59 THEN 1 ELSE 0 END) AS rpm_60_mins_plus "
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 AND "
						+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getCcmPatientsCount(Long clinicianId, Long orgId, Integer role, Date startDate,
			Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (role == 3) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN CCM_MINS < 20 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS > 59 THEN 1 ELSE 0 END) AS ccm_60_mins_plus"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 AND "
						+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN CCM_MINS < 20 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS > 59 THEN 1 ELSE 0 END) AS ccm_60_mins_plus"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 AND "
						+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPcmPatientsCount(Long clinicianId, Long orgId, Integer role, Date startDate,
			Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (role == 3) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN PCM_MINS < 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS > 59 THEN 1 ELSE 0 END) AS pcm_60_mins_plus "
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
						.setParameter("orgId", orgId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN PCM_MINS < 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS > 59 THEN 1 ELSE 0 END) AS pcm_60_mins_plus "
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 3  AND pr.IS_ACTIVATED = 1 AND "
						+ "  rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;

	}

	@Override
	public BigInteger dataCollection16DaysLessPatientCount(Long clinicianId, Long orgId, Integer role, Date st,
			Date ed) {
		Object num = 0;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				num = em.createNativeQuery(
						"SELECT COUNT(distinct rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
								+ "WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y' ) AND pr.FK_PROGRAM_ID = 1 and pr.IS_ACTIVATED = 1 "
								+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND rpc.DAYS_MEASURED < 16")
						.setParameter("orgId", orgId).setParameter("st", st).setParameter("ed", ed).getSingleResult();
			} else {
				if (clinicianId == 0) {
					num = em.createNativeQuery(
							"SELECT COUNT(distinct rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y') AND pr.FK_PROGRAM_ID = 1 and pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND rpc.DAYS_MEASURED < 16")
							.setParameter("orgId", orgId).setParameter("st", st).setParameter("ed", ed)
							.getSingleResult();
				} else {
					num = em.createNativeQuery(
							"SELECT COUNT(distinct rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y') AND pr.FK_PROGRAM_ID = 1 and pr.IS_ACTIVATED = 1 "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND rpc.DAYS_MEASURED < 16")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("st", st).setParameter("ed", ed).getSingleResult();
				}
			}

			if (num instanceof BigInteger) {
				return (BigInteger) num;
			} else if (num instanceof Number) {
				return BigInteger.valueOf(((Number) num).longValue());
			} else {
				throw new ClassCastException("Unexpected result type: " + num.getClass().getName());
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return BigInteger.ZERO;
	}

	@Override
	public BigInteger dataCollectionGreaterThan16DaysPatientCount(Long clinicianId, Long orgId, Integer role, Date st,
			Date ed) {
		Object num = 0;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				num = em.createNativeQuery(
						"SELECT COUNT(distinct rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
								+ "WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y' ) AND pr.FK_PROGRAM_ID = 1 and pr.IS_ACTIVATED = 1 "
								+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND rpc.DAYS_MEASURED >= 16")
						.setParameter("orgId", orgId).setParameter("st", st).setParameter("ed", ed).getSingleResult();
			} else {
				if (clinicianId == 0) {
					num = em.createNativeQuery(
							"SELECT COUNT(distinct rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y') AND pr.FK_PROGRAM_ID = 1 and pr.IS_ACTIVATED = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND rpc.DAYS_MEASURED >= 16")
							.setParameter("orgId", orgId).setParameter("st", st).setParameter("ed", ed)
							.getSingleResult();
				} else {
					num = em.createNativeQuery(
							"SELECT COUNT(distinct rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y') AND pr.FK_PROGRAM_ID = 1 and pr.IS_ACTIVATED = 1 "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND rpc.DAYS_MEASURED >= 16")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("st", st).setParameter("ed", ed).getSingleResult();
				}
			}

			if (num instanceof BigInteger) {
				return (BigInteger) num;
			} else if (num instanceof Number) {
				return BigInteger.valueOf(((Number) num).longValue());
			} else {
				throw new ClassCastException("Unexpected result type: " + num.getClass().getName());
			}

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return BigInteger.ZERO;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getRpmPatientsLessMinsCount(Long clinicianId, Long orgId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (clinicianId == 0) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS < 20 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 1  AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS < 20 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND(p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
						+ "  AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 1 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getCpmPatientsLessMinsCount(Long clinicianId, Long orgId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (clinicianId == 0) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN CCM_MINS < 20 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 2  AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN CCM_MINS < 20 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
						+ "  AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPcmPatientsLessMinsCount(Long clinicianId, Long orgId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			System.out.println(
					"CM ID:" + clinicianId + " orgId:" + orgId + " startDate:" + startDate + " endDate:" + endDate);
			if (clinicianId == 0) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN PCM_MINS < 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN PCM_MINS < 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
						+ "  AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getRpmPatientsCompletedMinsCount(Long clinicianId, Long orgId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (clinicianId == 0) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS >= 60 THEN 1 ELSE 0 END) AS rpm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 1  AND pr.IS_ACTIVATED = 1"
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS rpm_20_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS rpm_40_mins_count,"
						+ "  SUM(CASE WHEN RPM_MINS >= 60 THEN 1 ELSE 0 END) AS rpm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
						+ "  AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 1  AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getCcmPatientsCompletedMinsCount(Long clinicianId, Long orgId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			if (clinicianId == 0) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS >= 60 THEN 1 ELSE 0 END) AS ccm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 20 AND 39 THEN 1 ELSE 0 END) AS ccm_20_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS BETWEEN 40 AND 59 THEN 1 ELSE 0 END) AS ccm_40_mins_count,"
						+ "  SUM(CASE WHEN CCM_MINS >= 60 THEN 1 ELSE 0 END) AS ccm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
						+ "  AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 2 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPcmPatientsCompletedMinsCount(Long clinicianId, Long orgId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			System.out.println(
					"CM ID:" + clinicianId + " orgId:" + orgId + " startDate:" + startDate + " endDate:" + endDate);
			if (clinicianId == 0) {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN PCM_MINS BETWEEN 30 AND 59 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS >= 60 THEN 1 ELSE 0 END) AS pcm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("startDate", startDate).setParameter("endDate", endDate).getResultList();
			} else {
				patientsList = em.createNativeQuery("SELECT "
						+ "  SUM(CASE WHEN PCM_MINS >= 30 THEN 1 ELSE 0 END) AS pcm_30_mins_count,"
						+ "  SUM(CASE WHEN PCM_MINS >= 60 THEN 1 ELSE 0 END) AS pcm_60_mins_count"
						+ "  FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
						+ "  JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
						+ "  WHERE p.GROUP_ID =:orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
						+ "  AND pc.FK_CLINICIAN_ID = :clinicianId AND pr.FK_PROGRAM_ID = 3 AND pr.IS_ACTIVATED = 1 "
						+ "  AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)").setParameter("orgId", orgId)
						.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
						.setParameter("endDate", endDate).getResultList();
			}
		} finally {
			em.close();
		}
		return patientsList;

	}

	@Override
	public BigInteger dataCollection16DaysCompletedPatientCount(Long clinicianId, Long orgId, Integer role, Date st,
			Date ed) {

		Object num = 0;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (role == 3) {
				num = em.createNativeQuery(
						"SELECT COUNT(rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
								+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
								+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
								+ " WHERE p.GROUP_ID = :orgId AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 1 "
								+ " AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND DAYS_MEASURED < 16")
						.setParameter("orgId", orgId).setParameter("st", st).setParameter("ed", ed).getSingleResult();
			} else {
				if (clinicianId == 0) {
					num = em.createNativeQuery(
							"SELECT COUNT(rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId  AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND DAYS_MEASURED >= 16")
							.setParameter("orgId", orgId).setParameter("st", st).setParameter("ed", ed)
							.getSingleResult();
				} else {
					num = em.createNativeQuery(
							"SELECT COUNT(rpc.FK_PATIENT_ID) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId  AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') AND pr.FK_PROGRAM_ID = 1 "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.UPADATED_DATE BETWEEN (:st) AND (:ed) AND DAYS_MEASURED >= 16")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("st", st).setParameter("ed", ed).getSingleResult();
				}
			}
			if (num instanceof BigInteger) {
				return (BigInteger) num;
			} else if (num instanceof Number) {
				return BigInteger.valueOf(((Number) num).longValue());
			} else {
				throw new ClassCastException("Unexpected result type: " + num.getClass().getName());
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return BigInteger.ZERO;
	}

	@Override
	public Long getPatientsCountForBillingStats(Long orgId, Long clinicianId, String filterType, Date startDate,
			Date endDate) {

		Long count = 0l;
		Object patientCounts = 0l;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (clinicianId == 0) {
				if (filterType.equalsIgnoreCase("all")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm20Minless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm20Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 20 AND 39 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm40Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm60Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS >= 60 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm20Minless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm20Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 20 AND 39 "
									+ "AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm40Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm60Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS >= 60 "
									+ "AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm30Minless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS < 30 "
									+ "AND pr.FK_PROGRAM_ID = 3 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm30Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS BETWEEN 30 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 3 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm60Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS >= 60 "
									+ "AND pr.FK_PROGRAM_ID = 3 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("dayMeasuredless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED < 16 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("dayMeasured")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED >= 16 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				}
			} else {
				if (filterType.equalsIgnoreCase("all")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm20Minless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm20Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.RPM_MINS BETWEEN 20 AND 39 AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm40Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("rpm60Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.RPM_MINS >= 60 AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm20Minless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS < 20 "
									+ "AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm20Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.CCM_MINS BETWEEN 20 AND 39 AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm40Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ "AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("ccm60Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.CCM_MINS >= 60 AND pr.FK_PROGRAM_ID = 2 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				}

				else if (filterType.equalsIgnoreCase("pcm30Minless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.PCM_MINS < 30 "
									+ "AND pr.FK_PROGRAM_ID = 3 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm30Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ "AND rpc.PCM_MINS BETWEEN 30 AND 59 AND pr.FK_PROGRAM_ID = 3 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("pcm60Min")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.PCM_MINS BETWEEN >= 60 "
									+ "AND pr.FK_PROGRAM_ID = 3 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("dayMeasuredless")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.DAYS_MEASURED < 16 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				} else if (filterType.equalsIgnoreCase("dayMeasured")) {
					patientCounts = em
							.createNativeQuery("SELECT COUNT(*) FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ "JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ "WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ "AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.DAYS_MEASURED >= 16 "
									+ "AND pr.FK_PROGRAM_ID = 1 "
									+ "AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId)
							.setParameter("startDate", startDate).setParameter("endDate", endDate).getSingleResult();
					count = ((BigInteger) patientCounts).longValue();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			return 0l;
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPatientsForBillingStats(Long orgId, Long clinicianId, Integer index, Integer pageSize,
			String filterType, Date startDate, Date endDate) {

		List<Object[]> patients = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (clinicianId == 0) {
				if (filterType.equalsIgnoreCase("all")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("rpm20Minless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS < 20 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("rpm20Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 20 AND 39 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("rpm40Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS BETWEEN 40 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("rpm60Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.RPM_MINS >= 60 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("ccm20Minless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS < 20 "
									+ " AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("ccm20Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 20 AND 39 "
									+ " AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("ccm40Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS BETWEEN 40 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("ccm60Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.CCM_MINS >= 60 "
									+ " AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("pcm30Minless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS < 30 "
									+ " AND pr.FK_PROGRAM_ID = 3 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("pcm30Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS BETWEEN 30 AND 59 "
									+ " AND pr.FK_PROGRAM_ID = 3 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("pcm60Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.PCM_MINS >= 60 "
									+ " AND pr.FK_PROGRAM_ID = 3 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("dayMeasuredless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED < 16 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				} else if (filterType.equalsIgnoreCase("dayMeasured")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND rpc.DAYS_MEASURED >= 16 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setParameter("startDate", startDate).setParameter("endDate", endDate)
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.getResultList();
				}
			} else {
				if (filterType.equalsIgnoreCase("all")) {
					patients = em.createNativeQuery(
							/*
							 * "SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
							 * +
							 * " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
							 * + " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc " +
							 * " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID " +
							 * " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
							 * +
							 * " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
							 * +
							 * " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
							 * + " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							 */
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT p "
									+ " LEFT JOIN WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ "	AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate) "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm20Minless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.RPM_MINS < 20 AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm20Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.RPM_MINS BETWEEN 20 AND 39 AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm40Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.RPM_MINS BETWEEN 40 AND 59 AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("rpm60Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.RPM_MINS >= 60 "
									+ " AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm20Minless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.CCM_MINS < 20 AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm20Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.CCM_MINS BETWEEN 20 AND 39 AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm40Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.CCM_MINS BETWEEN 40 AND 59 AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("ccm60Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId AND rpc.CCM_MINS >= 60 "
									+ " AND pr.FK_PROGRAM_ID = 2 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm30Minless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.PCM_MINS < 30 AND pr.FK_PROGRAM_ID = 3 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm30Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.PCM_MINS BETWEEN 30 AND 59 AND pr.FK_PROGRAM_ID = 3 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("pcm60Min")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.PCM_MINS >= 60 AND pr.FK_PROGRAM_ID = 3 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("dayMeasuredless")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.DAYS_MEASURED < 16 AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				} else if (filterType.equalsIgnoreCase("dayMeasured")) {
					patients = em.createNativeQuery(
							"SELECT distinct p.PATIENT_ID, concat(p.FIRST_NAME,\" \",p.LAST_NAME) PatientName, "
									+ " p.PHONE_NUMBER, rpc.RPM_MINS, rpc.CCM_MINS, rpc.PCM_MINS, rpc.DAYS_MEASURED "
									+ " FROM WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
									+ " JOIN WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " JOIN WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
									+ " LEFT OUTER JOIN WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
									+ " JOIN WATCHRX_PATIENT_PROGRAMS pr ON pr.FK_PATIENT_ID = p.PATIENT_ID "
									+ " WHERE p.GROUP_ID = :orgId AND p.STATUS = 'Y' AND pc.FK_CLINICIAN_ID = :clinicianId "
									+ " AND rpc.DAYS_MEASURED >= 16 AND pr.FK_PROGRAM_ID = 1 "
									+ " AND rpc.UPADATED_DATE BETWEEN (:startDate) AND (:endDate)")
							.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
							.setParameter("clinicianId", clinicianId).setParameter("startDate", startDate)
							.setParameter("endDate", endDate).getResultList();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return patients;

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<CPTCodeReport> adminOrgCPTReports(List<Long> orgId, Long duration) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> reportList = null;

		try {
			String dateCondition;

			if (duration == 0) {
				// Current month: from 1st of this month to now
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()";
			} else if (duration == 1) {
				// Only last month
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN "
						+ "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND "
						+ "LAST_DAY(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
			} else {
				long safeDuration = Math.min(duration, 6);
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN " + "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL "
						+ (safeDuration - 1) + " MONTH), '%Y-%m-01') AND NOW()";
			}

			String queryStr = "SELECT clinicName, no_patient, "
					+ "SUM(devAss + rpm20 + rpm40 + rpm60 + ccm20 + ccm40 + ccm60 + pcm30 + pcm60 + dasyMea) as CPT_CODES, "
					+ "t1.tmonth, t1.tYear FROM ("
					+ "SELECT wg.GROUP_NAME as clinicName, count(distinct p.PATIENT_ID) as no_patient, "
					+ "SUM(CASE WHEN rpc.IS_DEVICE_ASSIGNED = 1 THEN 1 ELSE 0 END) AS devAss, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 20 THEN 1 ELSE 0 END) AS rpm20, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 40 THEN 1 ELSE 0 END) AS rpm40, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 60 THEN 1 ELSE 0 END) AS rpm60, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 20 THEN 1 ELSE 0 END) AS ccm20, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 40 THEN 1 ELSE 0 END) AS ccm40, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 60 THEN 1 ELSE 0 END) AS ccm60, "
					+ "SUM(CASE WHEN rpc.PCM_MINS >= 30 THEN 1 ELSE 0 END) AS pcm30, "
					+ "SUM(CASE WHEN rpc.PCM_MINS >= 60 THEN 1 ELSE 0 END) AS pcm60, "
					+ "SUM(CASE WHEN rpc.DAYS_MEASURED >= 16 THEN 1 ELSE 0 END) AS dasyMea, "
					+ "MONTHNAME(rpc.CREATED_DATE) as tmonth, YEAR(rpc.CREATED_DATE) as tYear "
					+ "FROM medsure.WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
					+ "JOIN medsure.WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_GROUP wg ON wg.GROUP_ID = p.GROUP_ID "
					+ "LEFT JOIN medsure.WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID " + "WHERE "
					+ dateCondition + " AND p.GROUP_ID IN (:orgId) " + "AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
					+ "GROUP BY clinicName, MONTHNAME(rpc.CREATED_DATE), YEAR(rpc.CREATED_DATE)) t1 "
					+ "GROUP BY t1.clinicName, t1.tmonth, t1.tYear ORDER BY t1.tYear DESC";

			reportList = em.createNativeQuery(queryStr).setParameter("orgId", orgId).getResultList();

		} finally {
			em.close();
		}
		return orgCPTReports(reportList);
	}

	private List<CPTCodeReport> orgCPTReports(List<Object[]> reportList) {
		List<CPTCodeReport> reports = new ArrayList<>();
		for (Object[] summaryObjt : reportList) {
			CPTCodeReport rep = new CPTCodeReport();
			// rep.setCaseManager(String.valueOf(summaryObjt[0]));
			rep.setClinic(String.valueOf(summaryObjt[0]));
			rep.setNoOfPatients(summaryObjt[1]);
			rep.setNoOfCPTCodes(summaryObjt[2]);
			rep.setMonth(String.valueOf(summaryObjt[3]));
			rep.setYear(String.valueOf(summaryObjt[4]));
			rep.setYearMonth(String.valueOf(summaryObjt[3]) + "-" + String.valueOf(summaryObjt[4]));
			reports.add(rep);
		}
		return reports;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<CPTCodeReport> adminClinicianCPTReports(List<Long> orgId, Long clinicanId, Long duration) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> reportList = null;
		try {
			String dateCondition = "";

			if (duration == 0) {
				// Current month: from 1st of this month to now
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()";
			} else if (duration == 1) {
				// Only last month
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN "
						+ "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND "
						+ "LAST_DAY(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
			} else {
				long safeDuration = Math.min(duration, 6);
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN " + "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL "
						+ (safeDuration - 1) + " MONTH), '%Y-%m-01') AND NOW()";
			}

			String query = "SELECT caseManager, clinicName, no_patient, SUM(devAss + rpm20 + rpm40 + rpm60 + ccm20 + ccm40 + ccm60 + pcm30 + pcm60 + dasyMea) as CPT_CODES, "
					+ "t1.tmonth, t1.tYear " + "FROM ("
					+ "SELECT concat(c.FIRST_NAME, c.LAST_NAME) as caseManager, wg.GROUP_NAME as clinicName, count(distinct p.PATIENT_ID) as no_patient, "
					+ "SUM(CASE WHEN rpc.IS_DEVICE_ASSIGNED = 1 THEN 1 ELSE 0 END) AS devAss, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 20 THEN 1 ELSE 0 END) AS rpm20, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 40 THEN 1 ELSE 0 END) AS rpm40, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 60 THEN 1 ELSE 0 END) AS rpm60, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 20 THEN 1 ELSE 0 END) AS ccm20, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 40 THEN 1 ELSE 0 END) AS ccm40, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 60 THEN 1 ELSE 0 END) AS ccm60, "
					+ "SUM(CASE WHEN rpc.PCM_MINS >= 30 THEN 1 ELSE 0 END) AS pcm30, "
					+ "SUM(CASE WHEN rpc.PCM_MINS >= 60 THEN 1 ELSE 0 END) AS pcm60, "
					+ "SUM(CASE WHEN rpc.DAYS_MEASURED >= 16 THEN 1 ELSE 0 END) AS dasyMea, "
					+ "MONTHNAME(rpc.CREATED_DATE) as tmonth, YEAR(rpc.CREATED_DATE) as tYear "
					+ "FROM medsure.WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
					+ "JOIN medsure.WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_GROUP wg on wg.GROUP_ID = p.GROUP_ID "
					+ "LEFT JOIN medsure.WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
					+ "JOIN medsure.WATCHRX_USER wu on c.FK_USER_ID = wu.USER_ID " + "WHERE " + dateCondition + " "
					+ "AND p.GROUP_ID IN (:orgId) " + "AND c.FK_USER_ID = :usrId "
					+ "AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') "
					+ "GROUP BY caseManager, clinicName, MONTHNAME(rpc.CREATED_DATE), YEAR(rpc.CREATED_DATE)) t1 "
					+ "GROUP BY t1.caseManager, t1.clinicName, t1.tmonth, t1.tYear ORDER BY t1.tYear DESC";

			reportList = em.createNativeQuery(query).setParameter("orgId", orgId).setParameter("usrId", clinicanId)
					.getResultList();

		} finally {
			em.close();
		}
		return clincicanCPTReports(reportList, true);
	}

	private List<CPTCodeReport> clincicanCPTReports(List<Object[]> reportList, boolean istotal) {
		List<CPTCodeReport> reports = new ArrayList<>();
		for (Object[] summaryObjt : reportList) {
			CPTCodeReport rep = new CPTCodeReport();
			rep.setCaseManager(String.valueOf(summaryObjt[0]));
			rep.setClinic(String.valueOf(summaryObjt[1]));
			rep.setNoOfPatients(summaryObjt[2]);
			rep.setNoOfCPTCodes(summaryObjt[3]);
			if (istotal) {
				rep.setMonth(String.valueOf(summaryObjt[4]));
				rep.setYear(String.valueOf(summaryObjt[5]));
				rep.setYearMonth(String.valueOf(summaryObjt[4]) + "-" + String.valueOf(summaryObjt[5]));
			}
			reports.add(rep);
		}

		return reports;
	}

	@Override
	@Transactional
	public void insertPatientDefaultValueEveryMonth() {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			em.createNativeQuery("INSERT\n" + "	INTO\n" + "	WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS (FK_PATIENT_ID) \n"
					+ "	SELECT\n" + "		p.PATIENT_ID\n" + "from\n" + "		WATCHRX_PATIENT p\n" + "where\n"
					+ "		p.STATUS = 'Y'\n" + "	and p.PATIENT_ID NOT IN (\n" + "	select\n"
					+ "		FK_PATIENT_ID\n" + "	from\n" + "		WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS\n" + "	where\n"
					+ "		YEAR(UPADATED_DATE) = YEAR(CURDATE())\n"
					+ "			AND MONTH(UPADATED_DATE) = MONTH(CURDATE()))").executeUpdate();
			em.getTransaction().commit();

		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
	}

	@Override
	public Long patientTotalCountInserted() {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = em.createQuery(
				"select count(*) from WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS where YEAR(UPADATED_DATE) = YEAR(CURDATE()) AND MONTH(UPADATED_DATE) = MONTH(CURDATE())");
		Long result = (Long) q.getSingleResult();
		return result;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<CPTCodeReport> adminClinicianTotalCPTReports(List<Long> orgId, Long clinicanId, Long duration) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> reportList = null;
		try {
			String dateCondition;

			if (duration == 0) {
				// Current month: from 1st of this month to now
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN DATE_FORMAT(NOW(), '%Y-%m-01') AND NOW()";
			} else if (duration == 1) {
				// Only last month
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN "
						+ "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND "
						+ "LAST_DAY(DATE_SUB(NOW(), INTERVAL 1 MONTH))";
			} else {
				// Last `duration` months including current month
				long safeDuration = Math.min(duration, 6);
				dateCondition = "DATE(rpc.CREATED_DATE) BETWEEN " + "DATE_FORMAT(DATE_SUB(NOW(), INTERVAL "
						+ (safeDuration - 1) + " MONTH), '%Y-%m-01') AND NOW()";
			}

			String query = "SELECT " + "caseManager, clinicName, no_patient, "
					+ "SUM(devAss + rpm20 + rpm40 + rpm60 + ccm20 + ccm40 + ccm60 + pcm30 + pcm60 + dasyMea) as CPT_CODES "
					+ "FROM (" + "SELECT concat(c.FIRST_NAME, c.LAST_NAME) as caseManager, "
					+ "wg.GROUP_NAME as clinicName, " + "COUNT(rpc.FK_PATIENT_ID) as no_patient, "
					+ "SUM(CASE WHEN rpc.IS_DEVICE_ASSIGNED = 1 THEN 1 ELSE 0 END) AS devAss, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 20 THEN 1 ELSE 0 END) AS rpm20, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 40 THEN 1 ELSE 0 END) AS rpm40, "
					+ "SUM(CASE WHEN rpc.RPM_MINS >= 60 THEN 1 ELSE 0 END) AS rpm60, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 20 THEN 1 ELSE 0 END) AS ccm20, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 40 THEN 1 ELSE 0 END) AS ccm40, "
					+ "SUM(CASE WHEN rpc.CCM_MINS >= 60 THEN 1 ELSE 0 END) AS ccm60, "
					+ "SUM(CASE WHEN rpc.PCM_MINS >= 30 THEN 1 ELSE 0 END) AS pcm30, "
					+ "SUM(CASE WHEN rpc.PCM_MINS >= 60 THEN 1 ELSE 0 END) AS pcm60, "
					+ "SUM(CASE WHEN rpc.DAYS_MEASURED >= 16 THEN 1 ELSE 0 END) AS dasyMea "
					+ "FROM medsure.WATCHRX_PATIENT_RPM_CCM_DATA_DETAILS rpc "
					+ "JOIN medsure.WATCHRX_PATIENT p ON rpc.FK_PATIENT_ID = p.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT pc ON pc.FK_PATIENT_ID = p.PATIENT_ID "
					+ "JOIN medsure.WATCHRX_GROUP wg ON wg.GROUP_ID = p.GROUP_ID "
					+ "LEFT JOIN medsure.WATCHRX_CLINICIAN c ON pc.FK_CLINICIAN_ID = c.CLINICIAN_ID "
					+ "JOIN medsure.WATCHRX_USER wu ON c.FK_USER_ID = wu.USER_ID WHERE " + dateCondition + " "
					+ "AND p.GROUP_ID IN (:orgId) " + "AND c.FK_USER_ID = :usrId "
					+ "AND (p.STATUS = 'Y' OR p.CONSENT_STATUS = 'D') GROUP BY caseManager, clinicName) t1 "
					+ "GROUP BY t1.caseManager, t1.clinicName";

			reportList = em.createNativeQuery(query).setParameter("orgId", orgId).setParameter("usrId", clinicanId)
					.getResultList();

		} finally {
			em.close();
		}
		return clincicanCPTReports(reportList, false);
	}

}
