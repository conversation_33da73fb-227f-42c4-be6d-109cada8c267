package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import javax.persistence.TemporalType;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientTextMessagesDAO;
import com.medsure.model.WatchRxPatientTextMessages;

@Component
public class WatchRxPatientTextMessagesDAOImpl extends BaseDAOImpl<WatchRxPatientTextMessages>
		implements WatchRxPatientTextMessagesDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageByPatientId(Long patientId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();

		try {
			data = em.createQuery("SELECT e FROM WatchRxPatientTextMessages e WHERE  e.watchrxPatient.patientId ="
					+ patientId + " ORDER BY e.updatedDate DESC").getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getNotSentMessages() {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();
		try {
			data = em.createQuery("SELECT e FROM WatchRxPatientTextMessages e WHERE  e.messageStatus = 'not_sent'")
					.getResultList();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@Override
	public void updateTextMessage(Long messageId, String messageStatus) {
		EntityManager em = entityManagerFactory.createEntityManager();
		em.getTransaction().begin();
		Query query = em.createQuery(
				"UPDATE WatchRxPatientTextMessages e set e.messageStatus =:messageStatus where e.questionId =:questionId ");
		query.setParameter("questionId", messageId);
		query.setParameter("messageStatus", messageStatus);
		query.executeUpdate();
		em.getTransaction().commit();
	}

	@Override
	public void updateTextMessage(Long patientId, Long questionId, String answer, Date updatedDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		em.getTransaction().begin();
		Query query = em.createQuery(
				"UPDATE WatchRxPatientTextMessages e set e.messageResponse =:answer , e.messageStatus = 'success' ,e.updatedDate =:updatedDate"
						+ " where (e.questionId =:questionId AND e.watchrxPatient.patientId =:patientId)");
		query.setParameter("patientId", patientId);
		query.setParameter("questionId", questionId);
		query.setParameter("answer", answer);
		query.setParameter("updatedDate", updatedDate, TemporalType.TIMESTAMP);
		query.executeUpdate();
		em.getTransaction().commit();

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageByPatientIdPaginated(Long patientId, Integer index,
			Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();

		try {
			data = em
					.createQuery("SELECT e FROM WatchRxPatientTextMessages e WHERE  e.watchrxPatient.patientId ="
							+ patientId + " ORDER BY e.updatedDate DESC")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@Override
	public Long getTextMessageByPatientIdResultCount(Long patientId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		Long count = null;

		try {
			count = (Long) em.createQuery(
					"SELECT COUNT(e) FROM WatchRxPatientTextMessages e WHERE  e.watchrxPatient.patientId =" + patientId)
					.getSingleResult();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT a FROM WatchRxPatientTextMessages a LEFT OUTER JOIN WatchRxPatientTextMessages e ON a.watchrxPatient.patientId = e.watchrxPatient.patientId AND ( a.createdDate < e.createdDate OR ( a.createdDate = e.createdDate AND  a.questionId < e.questionId ))  WHERE  e.watchrxPatient.patientId IS NULL and a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)");

		// get only the latest records of all patients belongs to caseManager
//		"SELECT t1.* FROM watchrx_patient_text_message AS t1 LEFT OUTER JOIN watchrx_patient_text_message AS t2 ON t1.FK_PATIENT_ID = t2.FK_PATIENT_ID AND (t1.CREATED_DATE < t2.CREATED_DATE OR (t1.CREATED_DATE = t2.CREATED_DATE AND t1.QUESTION_ID < t2.QUESTION_ID)) WHERE t2.FK_PATIENT_ID IS NULL ORDER BY t1.FK_PATIENT_ID"

		q.setParameter("clinicianId", clinicianId);
		List<WatchRxPatientTextMessages> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessagePhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT a FROM WatchRxPatientTextMessages a LEFT OUTER JOIN WatchRxPatientTextMessages e ON a.watchrxPatient.patientId = e.watchrxPatient.patientId AND ( a.createdDate < e.createdDate OR ( a.createdDate = e.createdDate AND  a.questionId < e.questionId ))  WHERE  e.watchrxPatient.patientId IS NULL and a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId = :physicianId)");

		q.setParameter("physicianId", physicianId);
		List<WatchRxPatientTextMessages> result = q.getResultList();

		return result;
	}

	@Override
	public Long getRespondedTextMessageCountCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT COUNT(a) FROM WatchRxPatientTextMessages a WHERE a.messageResponse <> 'Not yet received' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)");

		q.setParameter("clinicianId", clinicianId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@Override
	public Long getRespondedTextMessageCountPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT COUNT(a) FROM WatchRxPatientTextMessages a WHERE a.messageResponse <> 'Not yet received' AND a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId = :physicianId)");

		q.setParameter("physicianId", physicianId);
		Long result = (Long) q.getSingleResult();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageOnlyReceivedByPatientIdPaginated(Long patientId,
			Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();

		try {
			data = em
					.createQuery("SELECT e FROM WatchRxPatientTextMessages e WHERE  e.watchrxPatient.patientId ="
							+ patientId + " AND e.messageResponse <> 'Not yet received' ORDER BY e.updatedDate DESC")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@Override
	public Long getTextMessageOnlyReceivedByPatientIdCount(Long patientId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		Long data = 0L;

		try {
			data = (Long) em
					.createQuery("SELECT COUNT(e) FROM WatchRxPatientTextMessages e WHERE  e.watchrxPatient.patientId ="
							+ patientId + " AND e.messageResponse <> 'Not yet received'")
					.getSingleResult();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageOnlyReceivedByPatientIdForDate(Long patientId, Date date,
			Long scheduledTextMessageId) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();

		try {
			data = em.createQuery(
					"SELECT e FROM WatchRxPatientTextMessages e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxPatientScheduledTextMessages.scheduledTextMessagesId= :scheduledTextMessageId AND DATE(e.createdDate)= DATE(:date)")
					.setParameter("patientId", patientId).setParameter("scheduledTextMessageId", scheduledTextMessageId)
					.setParameter("date", date).getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageOnlyReceivedByPatientIdForDate(Long patientId, Date date,
			Long scheduledTextMessageId, String defaultTimeZone, String patientTimeZone) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();

		try {
			data = em.createQuery(
					"SELECT e FROM WatchRxPatientTextMessages e WHERE e.watchrxPatient.patientId = :patientId AND e.watchRxPatientScheduledTextMessages.scheduledTextMessagesId= :scheduledTextMessageId AND DATE(CONVERT_TZ(e.createdDate,:defaultTimeZone, :patientTimeZone))= DATE(:date)")
					.setParameter("patientId", patientId).setParameter("scheduledTextMessageId", scheduledTextMessageId)
					.setParameter("defaultTimeZone", defaultTimeZone).setParameter("patientTimeZone", patientTimeZone)
					.setParameter("date", date).getResultList();

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientTextMessages> getTextMessageOnlyReceivedByPatientIdDated(Long patientId, Date startDate,
			Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientTextMessages> data = new ArrayList<WatchRxPatientTextMessages>();

		try {
			data = em.createQuery(
					"SELECT e FROM WatchRxPatientTextMessages e WHERE e.watchrxPatient.patientId =:patientId AND e.messageResponse <> 'Not yet received' "
							+ "AND e.createdDate BETWEEN :startDate AND :endDate ")
					.setParameter("patientId", patientId).setParameter("startDate", startDate)
					.setParameter("endDate", endDate).getResultList();
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return data;
	}

	@Override
	public WatchRxPatientTextMessages getTextMessageByQuestionId(Long questionId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		WatchRxPatientTextMessages watchRxPatientTextMessages = null;
		try {
			watchRxPatientTextMessages = (WatchRxPatientTextMessages) em
					.createQuery("SELECT e FROM WatchRxPatientTextMessages e WHERE e.questionId = " + questionId)
					.getSingleResult();

		} catch (Exception e) {
			e.printStackTrace();
		}
		return watchRxPatientTextMessages;
	}
}
