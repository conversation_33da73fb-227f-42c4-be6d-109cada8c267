package com.medsure.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.TemporalType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.medsure.dao.WatchrxStoreOrderDAO;
import com.medsure.model.WatchrxStoreOrder;

@Component
public class WatchrxStoreOrderDAOImpl extends BaseDAOImpl<WatchrxStoreOrder> implements WatchrxStoreOrderDAO{
	private static final Logger logger = LoggerFactory.getLogger(WatchrxStoreOrderDAOImpl.class);

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxStoreOrder> getOrdersByDate(Date startDate, Date endDate) {
		List<WatchrxStoreOrder> allEvents = null;
		
		EntityManager em = entityManagerFactory.createEntityManager();

    	if(startDate!=null && endDate!=null){
			logger.info(startDate + "startDate" + endDate + "endDate");
			allEvents = em
					.createQuery(
							"SELECT e FROM WatchrxStoreOrder e WHERE e.createdDate BETWEEN :startDate AND :endDate")
					.setParameter("startDate", startDate, TemporalType.TIMESTAMP)
					.setParameter("endDate", endDate, TemporalType.TIMESTAMP).getResultList(); 
		}

		  return allEvents ;
	}

}
