package com.medsure.dao.impl;

import java.util.Iterator;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.common.Constants;
import com.medsure.dao.WatchrxWatchBuyingAllocationDAO;
import com.medsure.model.WatchrxWatchBuyingAllocation;

@Component
public class WatchrxWatchBuyingAllocationDAOImpl extends BaseDAOImpl<WatchrxWatchBuyingAllocation>
		implements WatchrxWatchBuyingAllocationDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedUnallocatedWatches() {
		// TODO Auto-generated method stub

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			Query query = em
					.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE watchAllocatedByAdmin IS NULL");
			list = query.getResultList();
		} finally {
			em.close();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedAllocatedWatches() {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			Query query = em.createQuery(
					"SELECT e FROM WatchrxWatchBuyingAllocation e WHERE watchAllocatedByAdmin IS NOT null");
			list = query.getResultList();
		} finally {
			em.close();
		}
		return list;
	}

	@Override
	public Boolean doesImeiOrNumberExist(String imei, String number) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query query = em.createQuery(
					"SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.watchAllocatedByAdmin IS NOT null");
			@SuppressWarnings("unchecked")
			List<WatchrxWatchBuyingAllocation> list = query.getResultList();
			Iterator<WatchrxWatchBuyingAllocation> listiterator = list.iterator();
			WatchrxWatchBuyingAllocation watchrxWatchBuyingAllocation;
			while (listiterator.hasNext()) {
				watchrxWatchBuyingAllocation = listiterator.next();
				if (watchrxWatchBuyingAllocation.getWatchAllocatedByAdmin().getWatchImeiNumber().equals(imei)
						|| watchrxWatchBuyingAllocation.getWatchAllocatedByAdmin().getWatchPhoneNumber()
								.equals(number)) {
					return true;
				}
			}
		} finally {
			em.close();
		}
		return false;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedUnallocatedWatchesPaginated(Integer index,
			Integer pageSize, Long orgId) {
		// TODO Auto-generated method stub

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			Query query = null;
			if (orgId == null || orgId == 0) {
				query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.UNALLOCATED + "'  ORDER BY e.updatedDate DESC");
				list = query.setFirstResult(index * pageSize).setMaxResults(pageSize).getResultList();
			} else {
				query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.UNALLOCATED
						+ "' AND e.group.groupId =:orgId ORDER BY e.updatedDate DESC");
				list = query.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
						.getResultList();
			}
		} finally {
			em.close();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedAllocatedWatchesPaginated(Integer index, Integer pageSize,
			Long orgId) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			Query query = null;
			if (orgId == null || orgId == 0) {
				query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED + "' ORDER BY e.updatedDate DESC");
				list = query.setFirstResult(index * pageSize).setMaxResults(pageSize).getResultList();
			} else {
				query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED
						+ "' AND e.group.groupId =:orgId ORDER BY e.updatedDate DESC");
				list = query.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("orgId", orgId)
						.getResultList();
			}
		} finally {
			em.close();
		}
		return list;
	}

	@Override
	public Long getAllPurchasedUnallocatedWatchesCount(Long orgId) {
		// TODO Auto-generated method stub

		EntityManager em = entityManagerFactory.createEntityManager();
		Long count;
		try {
			Query query = null;
			if (orgId == null || orgId == 0) {
				query = em.createQuery("SELECT COUNT(e) FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.UNALLOCATED + "'");
				count = (Long) query.getSingleResult();
			} else {
				query = em
						.createQuery("SELECT COUNT(e) FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
								+ Constants.WatchAllUnAllStatus.UNALLOCATED + "' AND e.group.groupId =:orgId")
						.setParameter("orgId", orgId);
				count = (Long) query.getSingleResult();
			}
		} finally {
			em.close();
		}
		return count;
	}

	@Override
	public Long getAllPurchasedAllocatedWatchesCount(Long orgId) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		Long count;
		try {
			Query query = null;
			if (orgId != null && orgId > 0) {
				query = em
						.createQuery("SELECT COUNT(e) FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
								+ Constants.WatchAllUnAllStatus.ALLOCATED + "' AND e.group.groupId =:orgId")
						.setParameter("orgId", orgId);
				count = (Long) query.getSingleResult();
			} else {
				query = em.createQuery("SELECT COUNT(e) FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED + "'");
				count = (Long) query.getSingleResult();
			}
		} finally {
			em.close();
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedUnallocatedWatchesByName(String nameOrImei, Long orgId) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			if (orgId != null && orgId > 0) {
				Query query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.UNALLOCATED
						+ "' AND ( e.group.groupName LIKE CONCAT(:nameOrImei,'%') ) "
						+ " AND e.group.groupId =:orgId");
				list = query.setParameter("nameOrImei", nameOrImei).setParameter("orgId", orgId).getResultList();
			} else {
				Query query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.UNALLOCATED
						+ "' AND e.group.groupName LIKE CONCAT(:nameOrImei,'%') ");
				list = query.setParameter("nameOrImei", nameOrImei).getResultList();

			}
		} finally {
			em.close();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedAllocatedWatchesCaseManagerNameOrImei(String nameOrImei,
			Long orgId) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			if (orgId != null && orgId > 0) {
				Query query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED
						+ "' AND ( e.group.groupName LIKE CONCAT(:nameOrImei,'%') OR watchAllocatedByAdmin.watchImeiNumber LIKE CONCAT(:nameOrImei,'%')) "
						+ " AND e.group.groupId =:orgId");
				list = query.setParameter("nameOrImei", nameOrImei).setParameter("orgId", orgId).getResultList();
			} else {
				Query query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED
						+ "' AND ( e.group.groupName LIKE CONCAT(:nameOrImei,'%') OR watchAllocatedByAdmin.watchImeiNumber LIKE CONCAT(:nameOrImei,'%')) ");
				list = query.setParameter("nameOrImei", nameOrImei).getResultList();

			}
		} finally {
			em.close();
		}
		return list;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchBuyingAllocation> getAllPurchasedAllocatedWatchesPaginated(Long orgId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchBuyingAllocation> list;
		try {
			Query query = null;
			if (orgId == null || orgId == 0) {
				query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED + "' ORDER BY e.updatedDate DESC");
				list = query.getResultList();
			} else {
				query = em.createQuery("SELECT e FROM WatchrxWatchBuyingAllocation e WHERE e.status='"
						+ Constants.WatchAllUnAllStatus.ALLOCATED
						+ "' AND e.group.groupId =:orgId ORDER BY e.updatedDate DESC");
				list = query.setParameter("orgId", orgId).getResultList();
			}
		} finally {
			em.close();
		}
		return list;
	}
}
