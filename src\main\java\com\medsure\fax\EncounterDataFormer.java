package com.medsure.fax;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.medsure.dao.EncountersNewDAO;
import com.medsure.dao.GroupDAO;
import com.medsure.dao.PatientDAO;
import com.medsure.model.WatchRxEncountersNew;
import com.medsure.model.WatchrxGroup;
import com.medsure.model.WatchrxPatient;
import com.medsure.ui.entity.server.FaxRequest;

@Component
public class EncounterDataFormer {

	@Autowired
	EncountersNewDAO encountersNewDAO;

	@Autowired
	GroupDAO groupDAO;

	@Autowired
	PatientDAO patientDAO;

	@Autowired
	SRFax srFax;

	static SimpleDateFormat formaterOnlyDate = new SimpleDateFormat("MM-dd-yyyy");
	SimpleDateFormat formater = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");
	private static final Logger logger = LoggerFactory.getLogger(SRFax.class);

	public void getEncounterdata(FaxRequest request) {
		/*
		 * List<Long> ids = new ArrayList<>(); ids.add(2666l); Calendar calendar =
		 * Calendar.getInstance(); calendar.add(Calendar.MONTH, 0);
		 * calendar.set(Calendar.DATE,
		 * calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		 * calendar.set(Calendar.HOUR_OF_DAY, 00); calendar.set(Calendar.MINUTE, 00);
		 * calendar.set(Calendar.SECOND, 00); Date monthFirstDay = calendar.getTime();
		 * calendar.set(Calendar.DATE,
		 * calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		 * calendar.set(Calendar.HOUR_OF_DAY, 23); calendar.set(Calendar.MINUTE, 59);
		 * calendar.set(Calendar.SECOND, 59); Date monthLastDay = calendar.getTime()
		 */;
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");

		List<WatchrxGroup> grps = groupDAO.findByProperty("isFaxActive", "Y");
		grps.forEach(grp -> {
			if (request.getOrgId().equals(grp.getGroupId())) {
				List<WatchrxPatient> patients = patientDAO.getAllPatientsByOrgId(grp.getGroupId());
				if (request.getPatientId() != null && !request.getPatientId().isEmpty()) {
					patients = patientDAO.findByIdInArray("patientId", request.getPatientId());
				} else {
					patients = patientDAO.getAllPatientsByOrgId(grp.getGroupId());
				}
				patients.forEach(pat -> {
					// if(ids.contains(pat.getPatientId())) {
					Map<String, Object> coversheet = new HashMap<>();
					coversheet.put("providerName",
							pat.getWatchrxPhysician().getFirstName() + " " + pat.getWatchrxPhysician().getLastName());
					coversheet.put("orgName", grp.getGroupName());
					coversheet.put("orgFaxNumber", grp.getGroupFax());
					coversheet.put("patientName", pat.getFirstName() + " " + pat.getLastName());
					coversheet.put("phoneNumber", pat.getPhoneNumber() == null ? "N/A" : pat.getPhoneNumber());
					coversheet.put("dob", formaterOnlyDate.format(pat.getDob()));
					coversheet.put("age", calculateAge(formaterOnlyDate.format(pat.getDob()), "MM-dd-yyyy"));
					coversheet.put("frmFaxNumber", "**********");
					String address = pat.getWatchrxAddress().getAddress1() + ", " + pat.getWatchrxAddress().getCity()
							+ ", " + ", " + pat.getWatchrxAddress().getState() + ", "
							+ pat.getWatchrxAddress().getCountry() + ", " + pat.getWatchrxAddress().getZip();
					coversheet.put("address", address);
					List<Map<String, Object>> entList = new ArrayList<>();
					List<WatchRxEncountersNew> encounters = null;
					try {
						encounters = encountersNewDAO.reasonByPatientId(pat.getPatientId(), "all",
								format.parse(request.getStartDate()), format.parse(request.getEndDate()));
					} catch (ParseException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					if (encounters != null && !encounters.isEmpty()) {
						encounters.forEach(en -> {
							Map<String, Object> encounterMap = new HashMap<>();
							encounterMap.put("reason", en.getEncounterReason());
							encounterMap.put("date", formater.format(en.getEncounterDatetime()));
							encounterMap.put("notes", en.getEncounterDescription());
							encounterMap.put("mins", String.valueOf(en.getDuration()));
							entList.add(encounterMap);
						});
						try {
							PdfGeneratorService pdfGeneratorService = new PdfGeneratorService(
									ThymeleafUtil.createTemplateEngine());
							byte[] file = pdfGeneratorService.generateBatchPdf(coversheet, entList);
							String fileName = pat.getFirstName() + "-" + pat.getLastName().replaceAll("\\s+", "");
							//File fo = new File("D:\\" + fileName + ".pdf");
							//FileUtils.writeByteArrayToFile(fo, file);
							 srFax.sendFax(file,fileName+".pdf", grp.getGroupFax());
						} catch (Exception e) {
							logger.error("failed to generate PDF/Fax" + e);
						}
					}
				});
			}
		});
	}
	@Async
	public void getEncounterdataForAll() {
		  Calendar calendar =
		  Calendar.getInstance(); calendar.add(Calendar.MONTH, 0);
		  calendar.set(Calendar.DATE,
		  calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
		  calendar.set(Calendar.HOUR_OF_DAY, 00); calendar.set(Calendar.MINUTE, 00);
		  calendar.set(Calendar.SECOND, 00); Date monthFirstDay = calendar.getTime();
		  calendar.set(Calendar.DATE,
		  calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
		  calendar.set(Calendar.HOUR_OF_DAY, 23); calendar.set(Calendar.MINUTE, 59);
		  calendar.set(Calendar.SECOND, 59); Date monthLastDay = calendar.getTime()
		 ;
		new SimpleDateFormat("yyyy-MM-dd");

		List<WatchrxGroup> grps = groupDAO.findByProperty("isFaxActive", "Y");
		grps.forEach(grp -> {
			//if (request.getOrgId().equals(grp.getGroupId())) {
				List<WatchrxPatient> patients = patientDAO.getAllPatientsByOrgId(grp.getGroupId());
					patients = patientDAO.getAllPatientsByOrgId(grp.getGroupId());
				patients.forEach(pat -> {
					// if(ids.contains(pat.getPatientId())) {
					Map<String, Object> coversheet = new HashMap<>();
					coversheet.put("providerName",
							pat.getWatchrxPhysician().getFirstName() + " " + pat.getWatchrxPhysician().getLastName());
					coversheet.put("orgName", grp.getGroupName());
					coversheet.put("orgFaxNumber", grp.getGroupFax());
					coversheet.put("patientName", pat.getFirstName() + " " + pat.getLastName());
					coversheet.put("phoneNumber", pat.getPhoneNumber() == null ? "N/A" : pat.getPhoneNumber());
					coversheet.put("dob", formaterOnlyDate.format(pat.getDob()));
					coversheet.put("age", calculateAge(formaterOnlyDate.format(pat.getDob()), "MM-dd-yyyy"));
					coversheet.put("frmFaxNumber", "**********");
					String address = pat.getWatchrxAddress().getAddress1() + ", " + pat.getWatchrxAddress().getCity()
							+ ", " + ", " + pat.getWatchrxAddress().getState() + ", "
							+ pat.getWatchrxAddress().getCountry() + ", " + pat.getWatchrxAddress().getZip();
					coversheet.put("address", address);
					List<Map<String, Object>> entList = new ArrayList<>();
					List<WatchRxEncountersNew> encounters = null;
					try {
						encounters = encountersNewDAO.reasonByPatientId(pat.getPatientId(), "all",
								monthFirstDay, monthLastDay);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					if (encounters != null && !encounters.isEmpty()) {
						encounters.forEach(en -> {
							Map<String, Object> encounterMap = new HashMap<>();
							encounterMap.put("reason", en.getEncounterReason());
							encounterMap.put("date", formater.format(en.getEncounterDatetime()));
							encounterMap.put("notes", en.getEncounterDescription());
							encounterMap.put("mins", String.valueOf(en.getDuration()));
							entList.add(encounterMap);
						});
						try {
							PdfGeneratorService pdfGeneratorService = new PdfGeneratorService(
									ThymeleafUtil.createTemplateEngine());
							byte[] file = pdfGeneratorService.generateBatchPdf(coversheet, entList);
							String fileName = pat.getFirstName() + "-" + pat.getLastName().replaceAll("\\s+", "");
							//File fo = new File("D:\\" + fileName + ".pdf");
							//FileUtils.writeByteArrayToFile(fo, file);
							 srFax.sendFax(file,fileName+".pdf", grp.getGroupFax());
						} catch (Exception e) {
							logger.error("failed to generate PDF/Fax" + e);
						}
					}
				});
			//}
		});
	}

	private static int calculateAge(String dobString, String format) {
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
		LocalDate dob = LocalDate.parse(dobString, formatter);
		LocalDate today = LocalDate.now();

		return Period.between(dob, today).getYears();
	}

}
