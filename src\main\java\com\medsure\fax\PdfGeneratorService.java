package com.medsure.fax;
import java.io.ByteArrayOutputStream;
import java.util.List;
import java.util.Map;

import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;
import org.xhtmlrenderer.pdf.ITextRenderer;
public class PdfGeneratorService {

    private final TemplateEngine templateEngine;

    public PdfGeneratorService(TemplateEngine templateEngine) {
        this.templateEngine = templateEngine;
    }

    public byte[] generateBatchPdf(Map<String, Object> coversheetData, List<Map<String, Object>> encountersData) throws Exception {
    	Context context = new Context();
        context.setVariable("coversheet", coversheetData);
        context.setVariable("encounter", encountersData); // list of maps

        // Render single, valid XHTML doc
        String finalHtml = templateEngine.process("encounter-main", context);
        return renderPdf(finalHtml);
    }

    private byte[] renderPdf(String html) throws Exception {
        try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            ITextRenderer renderer = new ITextRenderer();
            renderer.setDocumentFromString(html);
            renderer.layout();
            renderer.createPDF(outputStream);
            return outputStream.toByteArray();
        }
    }
}
