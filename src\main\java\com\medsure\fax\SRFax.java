package com.medsure.fax;

import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Base64;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
@Service
public class SRFax {
	
	private static final Logger logger = LoggerFactory.getLogger(SRFax.class);


    public void sendFax(byte[] file, String fileName, String toFax) throws IOException {
        // Set credentials and fax details
        String accessId = "402124";
        String accessPwd = "W@tchfax2025!";
        String senderEmail = "<EMAIL>";
        String base64Content = Base64.getEncoder().encodeToString(file);

        // Build JSON payload
        String body = "action=" + URLEncoder.encode("Queue_Fax", "UTF-8")
        + "&access_id=" + URLEncoder.encode(accessId, "UTF-8")
        + "&access_pwd=" + URLEncoder.encode(accessPwd, "UTF-8")
        + "&sSenderEmail=" + URLEncoder.encode(senderEmail, "UTF-8")
        + "&sToFaxNumber=" + URLEncoder.encode("1"+toFax, "UTF-8")
        + "&sCallerID=" + URLEncoder.encode(toFax, "UTF-8")
        + "&sFileName_1=" + URLEncoder.encode(fileName, "UTF-8")
        + "&sFaxType=" + URLEncoder.encode("SINGLE", "UTF-8")
        + "&sFileContent_1=" + URLEncoder.encode(base64Content, "UTF-8");

        // Send POST request
        URL url = new URL("https://www.srfax.com/SRF_SecWebSvc.php");
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            os.write(body.getBytes());
        }

        // Read response
        int status = conn.getResponseCode();
        String response = new String(conn.getInputStream().readAllBytes());

        logger.info("HTTP " + status);
        logger.info("SRFax Response:\n" + response);
    }
}

