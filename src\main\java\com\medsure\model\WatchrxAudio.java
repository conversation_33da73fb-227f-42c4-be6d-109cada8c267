package com.medsure.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "WATCHRX_AUDIO")
public class WatchrxAudio extends Auditable<String> implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	private Long audioId;

	@Column(name = "AUDIO_FILE_PATH", nullable = false, length = 256)
	private String audioFilePath;

	@Column(name = "AUDIO_TEXT", columnDefinition = "LONGTEXT")
	private String audioText;
	
	@Column(name = "SUMMARIZE_AUDIO_TEXT", columnDefinition = "LONGTEXT")
	private String summarizeAudioText;

	@Column(name = "VALIDATION_CHECKS", columnDefinition = "LONGTEXT")
	private String validationChecks;

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FK_PATIENT_ID", nullable = false)
	private WatchrxPatient watchrxPatient;
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FK_GROUP_ID")
	private WatchrxGroup watchrxGroup;

	public Long getAudioId() {
		return audioId;
	}

	public void setAudioId(Long audioId) {
		this.audioId = audioId;
	}

	public String getAudioFilePath() {
		return audioFilePath;
	}

	public void setAudioFilePath(String audioFilePath) {
		this.audioFilePath = audioFilePath;
	}

	public String getAudioText() {
		return audioText;
	}

	public void setAudioText(String audioText) {
		this.audioText = audioText;
	}

	public WatchrxPatient getWatchrxPatient() {
		return watchrxPatient;
	}

	public void setWatchrxPatient(WatchrxPatient watchrxPatient) {
		this.watchrxPatient = watchrxPatient;
	}

	public WatchrxGroup getWatchrxGroup() {
		return watchrxGroup;
	}

	public void setWatchrxGroup(WatchrxGroup watchrxGroup) {
		this.watchrxGroup = watchrxGroup;
	}
	public String getSummarizeAudioText() {
		return summarizeAudioText;
	}

	public void setSummarizeAudioText(String summarizeAudioText) {
		this.summarizeAudioText = summarizeAudioText;
	}

	public void setValidationChecks(String validationChecks) {
		this.validationChecks = validationChecks;
	}

}
