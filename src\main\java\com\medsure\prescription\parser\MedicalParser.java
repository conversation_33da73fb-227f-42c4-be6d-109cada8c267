package com.medsure.prescription.parser;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.cloud.vertexai.VertexAI;
import com.google.cloud.vertexai.api.GenerateContentResponse;
import com.google.cloud.vertexai.api.HarmCategory;
import com.google.cloud.vertexai.api.SafetySetting;
import com.google.cloud.vertexai.generativeai.GenerativeModel;
import com.google.cloud.vision.v1.AnnotateImageRequest;
import com.google.cloud.vision.v1.AnnotateImageResponse;
import com.google.cloud.vision.v1.BatchAnnotateImagesResponse;
import com.google.cloud.vision.v1.Feature;
import com.google.cloud.vision.v1.Image;
import com.google.cloud.vision.v1.ImageAnnotatorClient;
import com.google.protobuf.ByteString;

public class MedicalParser {

    // --- OCR Step: Extract text from image using Google Cloud Vision API ---
	 public static String detectTextFromImage(InputStream imageInputStream) throws IOException {
	        List<AnnotateImageRequest> requests = new ArrayList<>();

	        // Read the InputStream into a ByteString
	        // It's crucial to correctly read the entire content of the InputStream.
	        ByteString imgBytes = null;
	        try (ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {
	            int nRead;
	            byte[] data = new byte[4096]; // Read in chunks
	            while ((nRead = imageInputStream.read(data, 0, data.length)) != -1) {
	                buffer.write(data, 0, nRead);
	            }
	            imgBytes = ByteString.copyFrom(buffer.toByteArray());
	        } catch (IOException e) {
	            System.err.println("Error reading image from InputStream: " + e.getMessage());
	            throw e; // Re-throw to be caught by the calling method
	        }

	        if (imgBytes == null || imgBytes.isEmpty()) {
	            System.err.println("Image input stream was empty or could not be read.");
	            return null;
	        }

	        Image img = Image.newBuilder().setContent(imgBytes).build();
	        Feature feat = Feature.newBuilder().setType(Feature.Type.TEXT_DETECTION).build();
	        AnnotateImageRequest request =
	            AnnotateImageRequest.newBuilder().addFeatures(feat).setImage(img).build();
	        requests.add(request);

	        try (ImageAnnotatorClient client = ImageAnnotatorClient.create()) {
	            BatchAnnotateImagesResponse response = client.batchAnnotateImages(requests);
	            if (response.getResponsesCount() > 0) {
	                AnnotateImageResponse firstResponse = response.getResponses(0);
	                if (firstResponse.hasFullTextAnnotation()) {
	                    System.out.println("OCR successful. Extracted text length: " + firstResponse.getFullTextAnnotation().getText().length());
	                    return firstResponse.getFullTextAnnotation().getText();
	                } else if (firstResponse.hasError()) {
	                    System.err.println("Vision API error during text detection: " + firstResponse.getError().getMessage());
	                    return null; // Indicate failure
	                }
	            }
	            System.out.println("OCR failed or no text found by Vision API.");
	            return ""; // Return empty string if no text is found or response is empty
	        }
	    }


    // --- NLP Step: Parse extracted text using Vertex AI (MedLM/Gemini) ---
    public static String parsePrescriptionWithVertexAI(String prescriptionText, String projectId, String location) throws IOException {
        try (VertexAI vertexAi = new VertexAI(projectId, location)) {
            // Using 'gemini-1.5-flash' as a general-purpose model, which is good for structured extraction.
            // If you have access to a specific MedLM model and it's tuned for this, you could try 'medlm-large' or similar.
            GenerativeModel model = new GenerativeModel("gemini-2.5-pro", vertexAi);

            // Craft a precise prompt. This is crucial for getting good JSON output.
            String prompt = "Parse the following medical prescription text and extract the following details into a JSON object. " +
                            "For each medication, include 'name', 'strength', 'form', 'dosage', 'frequency', 'route', 'duration', 'quantity', 'instructions', 'commonName', 'refills' , 'quantityPerDay' (representing the total numerical amount to be taken in 24 hours, **as a number only, without units like 'tablets' or 'capsules'**).. " +
                            "**and 'quantityAtTime' (representing the numerical amount to be taken in a single dose, as a number only, without units)**. " +
                            "Also extract 'patient' details (name, dateOfBirth, gender), 'prescriber' details (name, licenseNumber, contactInfo), 'prescriptionDate', 'refillsTotal', 'notes', and 'pharmacyInformation' (name, location, zipCode, phone). " +
                            "If a field is not found, use \"N/A\". Ensure all keys are present. Return only the JSON object. \n\n" +
                            "Prescription Text:\n" + prescriptionText + "\n\nJSON Output:";

            Arrays.asList(
                SafetySetting.newBuilder()
                    .setCategory(HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT)
                    .setThreshold(SafetySetting.HarmBlockThreshold.BLOCK_NONE)
                    .build(),
                SafetySetting.newBuilder()
                    .setCategory(HarmCategory.HARM_CATEGORY_HARASSMENT)
                    .setThreshold(SafetySetting.HarmBlockThreshold.BLOCK_NONE)
                    .build(),
                SafetySetting.newBuilder()
                    .setCategory(HarmCategory.HARM_CATEGORY_HATE_SPEECH)
                    .setThreshold(SafetySetting.HarmBlockThreshold.BLOCK_NONE)
                    .build(),
                SafetySetting.newBuilder()
                    .setCategory(HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT)
                    .setThreshold(SafetySetting.HarmBlockThreshold.BLOCK_NONE)
                    .build()
            );

            GenerateContentResponse response = model.generateContent(prompt);

            if (response.getCandidatesCount() > 0 && response.getCandidates(0).getContent().getPartsCount() > 0) {
                String jsonResponse = response.getCandidates(0).getContent().getParts(0).getText();
                System.out.println("Vertex AI NLP successful. Raw JSON output:" + jsonResponse);
                return jsonResponse;
            } else {
                System.out.println("Vertex AI NLP failed or no content generated.");
                return "{}"; // Return empty JSON on failure
            }
        }
    }

    public PatientMedicationModel  getData(InputStream imagePath) {
        // --- Configuration ---
        // IMPORTANT: Replace with your actual Google Cloud Project ID and Vertex AI region.
        String projectId = "watchrx-1007"; // e.g., "my-gcp-project-12345"
        String location = "us-central1";      // e.g., "us-central1" or "asia-southeast1"



        try {
            // Step 1: Perform OCR on the image
            System.out.println("Starting OCR for image: " + imagePath);
            String extractedText = detectTextFromImage(imagePath);

            if (extractedText.isEmpty()) {
                System.out.println("No text was extracted by OCR. Cannot proceed with parsing.");
                return null;
            }

            System.out.println("\n--- Extracted Text (from OCR) ---");
            System.out.println("TEXT OCR"+extractedText);
            System.out.println("--------------------------------\n");

            // Step 2: Use Vertex AI to parse the extracted text into JSON
            System.out.println("Sending extracted text to Vertex AI for NLP parsing...");
            String parsedJsonString = parsePrescriptionWithVertexAI(extractedText, projectId, location);

         // Define a regex pattern to find JSON within markdown code blocks
         // This pattern looks for "```json" followed by any characters (non-greedy)
         // then "```", capturing the content in between.
         Pattern pattern = Pattern.compile("```json\\s*([\\s\\S]*?)\\s*```");
         Matcher matcher = pattern.matcher(parsedJsonString);

         String cleanJsonString = parsedJsonString;
         if (matcher.find()) {
        	    cleanJsonString = matcher.group(1); // Extract the content from the first group
        	    System.out.println("Extracted JSON from markdown block."+cleanJsonString);
        	} else {
        	    System.out.println("No '```json' markdown block found. Assuming entire output is JSON.");
        	}
            System.out.println("\n--- Final Parsed JSON Output ---"+cleanJsonString);
            // Optional: Pretty print the JSON for better readability
            ObjectMapper mapper = new ObjectMapper();
           // mapper.enable(SerializationFeature.INDENT_OUTPUT);
            try {
            	PatientMedicationModel json = mapper.readValue(cleanJsonString, PatientMedicationModel.class);
            return json;
            } catch (Exception e) {
                System.err.println("Failed to pretty-print JSON. Raw output:" + cleanJsonString);
                System.err.println("JSON Parsing Error: " + e.getMessage());
                e.printStackTrace();
            }

        } catch (IOException e) {
            System.err.println("An I/O error occurred: " + e.getMessage());
            e.printStackTrace();
        } catch (Exception e) {
            System.err.println("An unexpected error occurred: " + e.getMessage());
            e.printStackTrace();
        }
		return null;
    }
    
}
