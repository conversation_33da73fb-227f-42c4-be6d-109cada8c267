package com.medsure.prescription.parser;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
@JsonIgnoreProperties(ignoreUnknown = true)
public class PatientMedicationModel {
	
	public Map<String, String> getPatient() {
		return patient;
	}

	public void setPatient(Map<String, String> patient) {
		this.patient = patient;
	}

	public Map<String, String> getPrescriber() {
		return prescriber;
	}

	public void setPrescriber(Map<String, String> prescriber) {
		this.prescriber = prescriber;
	}

	public String getPrescriptionDate() {
		return prescriptionDate;
	}

	public void setPrescriptionDate(String prescriptionDate) {
		this.prescriptionDate = prescriptionDate;
	}

	public List<PrescriptionModel> getMedications() {
		return medications;
	}

	public void setMedications(List<PrescriptionModel> medications) {
		this.medications = medications;
	}

	public Map<String, String> getPharmacyInformation() {
		return pharmacyInformation;
	}

	public void setPharmacyInformation(Map<String, String> pharmacyInformation) {
		this.pharmacyInformation = pharmacyInformation;
	}

	private Map<String, String> patient;
	
	private Map<String, String> prescriber;
	
	private String prescriptionDate;
	
	private List<PrescriptionModel> medications;
	
	private Map<String,String> pharmacyInformation;

}
