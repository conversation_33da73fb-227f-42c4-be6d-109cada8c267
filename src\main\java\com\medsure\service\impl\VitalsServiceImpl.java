package com.medsure.service.impl;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Logger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.medsure.common.Constants;
import com.medsure.dao.PatientVitalDAO;
import com.medsure.dao.PedometerReadingsDAO;
import com.medsure.factory.WatchRxFactory;
import com.medsure.model.WatchRxPatientPedometerReadings;
import com.medsure.model.WatchRxPatientVital;
import com.medsure.service.PatientService;
import com.medsure.service.VitalsService;
import com.medsure.ui.entity.server.PatientVO;
import com.medsure.ui.entity.server.ThresholdConfigVO;

@Service
public class VitalsServiceImpl implements VitalsService {

	private static final Logger log = Logger.getLogger(VitalsServiceImpl.class.getName());

	@Autowired
	PatientVitalDAO patientVitalDAO;

	@Autowired
	PedometerReadingsDAO pedometerReadingsDAO;

	private SimpleDateFormat dateFormatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Override
	public Map<String, Object> getLatest5VitalsForPatient(Long patientId) {
		return getLatestNVitalsForPatient(patientId, 5);
	}

	@Override
	public Map<String, Object> getLatestNVitalsForPatient(Long patientId, Integer count) {
		Map<String, Object> vitalsData = new HashMap<>();

		try {
			PatientService patientService = WatchRxFactory.getPatientService();

			// Get threshold configuration to check which vitals are enabled
			PatientVO tempPatient = patientService.getPatient(patientId);

			ThresholdConfigVO minMaxValues = tempPatient.getThresholdMinMax();

			if (tempPatient != null && tempPatient.getThresholdData() != null) {

				// Heart Rate
				if (tempPatient.getThresholdData().getHeartRateIsEnabled() != null
						&& tempPatient.getThresholdData().getHeartRateIsEnabled()) {
					vitalsData.put("Heart Rate", getVitalTypeData(patientId, Constants.VitalTypes.HEARTRATE, count,
							minMaxValues.getHeartRateCriticalMin(), minMaxValues.getHeartRateCriticalMax()));
				}

				// Oxygen Saturation (SPO2)
				if (tempPatient.getThresholdData().getSpo2IsEnabled() != null
						&& tempPatient.getThresholdData().getSpo2IsEnabled()) {
					vitalsData.put("Oxygen Saturation", getVitalTypeData(patientId, Constants.VitalTypes.SPO2, count,
							minMaxValues.getSpo2CriticalMin(), minMaxValues.getSpo2CriticalMax()));
				}

				// Blood Sugar (combined)
				if (tempPatient.getThresholdData().getBloodSugarIsEnabled() != null
						&& tempPatient.getThresholdData().getBloodSugarIsEnabled()) {
					Map<String, Object> bloodSugarData = getBloodSugarData(patientId, count,
							minMaxValues.getBloodSugarCriticalMin(), minMaxValues.getBloodSugarCriticalMax());
					vitalsData.put("Blood Sugar", bloodSugarData);
				}

				// Temperature
				if (tempPatient.getThresholdData().getTemperatureIsEnabled() != null
						&& tempPatient.getThresholdData().getTemperatureIsEnabled()) {
					vitalsData.put("Temperature", getVitalTypeData(patientId, Constants.VitalTypes.TEMPERATURE, count,
							minMaxValues.getTemperatureCriticalMin(), minMaxValues.getTemperatureCriticalMax()));
				}

				// Weight
				if (tempPatient.getThresholdData().getWeightIsEnabled() != null
						&& tempPatient.getThresholdData().getWeightIsEnabled()) {
					vitalsData.put("Weight", getVitalTypeData(patientId, Constants.VitalTypes.WEIGHT, count,
							minMaxValues.getWeightCriticalMin(), minMaxValues.getWeightCriticalMax()));
				}

				// Blood Pressure (combined systolic and diastolic)
				if ((tempPatient.getThresholdData().getSystolicBloodPressureIsEnabled() != null
						&& tempPatient.getThresholdData().getSystolicBloodPressureIsEnabled())
						|| (tempPatient.getThresholdData().getDiastolicBloodPressureIsEnabled() != null
								&& tempPatient.getThresholdData().getDiastolicBloodPressureIsEnabled())) {
					Map<String, Object> bloodPressureData = getBloodPressureData(patientId, count,
							minMaxValues.getSystolicBloodPressureCriticalMin(),
							minMaxValues.getSystolicBloodPressureCriticalMax(),
							minMaxValues.getDiastolicBloodPressureCriticalMin(),
							minMaxValues.getDiastolicBloodPressureCriticalMax());
					vitalsData.put("Blood Pressure", bloodPressureData);
				}

				// Pedometer
				if (tempPatient.getThresholdData().getPedometerStepIsEnabled() != null
						&& tempPatient.getThresholdData().getPedometerStepIsEnabled()) {
					vitalsData.put("Pedometer",
							getPedometerData(patientId, count, minMaxValues.getPedometerStepCountCriticalMin(),
									minMaxValues.getPedometerStepCountCriticalMax()));
				}
			}
		} catch (Exception e) {
			log.severe("Error getting latest " + count + " vitals for patient " + patientId + ": " + e.getMessage());
		}

		return vitalsData;
	}

//	@Override
//	public Map<String, Object> getLatest5VitalsForType(Long patientId, String vitalType) {
//		return getVitalTypeData(patientId, vitalType, 5);
//	}

	private Map<String, Object> getVitalTypeData(Long patientId, String vitalType, Integer count, Double min,
			Double max) {
		Map<String, Object> vitalTypeData = new HashMap<>();
		List<Map<String, Object>> values = new ArrayList<>();

		try {
			List<WatchRxPatientVital> vitalReadings = patientVitalDAO.getVitalReadingsForPatientVitalType(patientId,
					vitalType, 0, count);

			for (WatchRxPatientVital vital : vitalReadings) {
				Map<String, Object> valueEntry = new HashMap<>();
				Double vitalValue = vital.getVitalValue();
				// Convert to integer (round to nearest whole number)
				Integer intValue = vitalValue != null ? (int) Math.round(vitalValue) : 0;
				valueEntry.put("value", intValue);
				valueEntry.put("datetime", dateFormatter.format(vital.getMeasuredDateTime()));
				values.add(valueEntry);
			}

		} catch (Exception e) {
			log.severe("Error getting vital data for type " + vitalType + ": " + e.getMessage());
		}

		vitalTypeData.put("values", values);
		// Convert min and max to integers
		vitalTypeData.put("min", min != null ? (int) Math.round(min) : null);
		vitalTypeData.put("max", max != null ? (int) Math.round(max) : null);

		return vitalTypeData;
	}

	private Map<String, Object> getBloodSugarData(Long patientId, Integer count, Double min, Double max) {
		Map<String, Object> bloodSugarData = new HashMap<>();

		try {
			// Get fasting blood sugar data
			Map<String, Object> fastingData = getVitalTypeData(patientId, Constants.VitalTypes.FASTING_BLOOD_SUGAR,
					count, min, max);

			// Get random blood sugar data
			Map<String, Object> randomData = getVitalTypeData(patientId, Constants.VitalTypes.RANDOM_BLOOD_SUGAR, count,
					min, max);

			bloodSugarData.put("fasting", fastingData);
			bloodSugarData.put("random", randomData);

		} catch (Exception e) {
			log.severe("Error getting blood sugar data: " + e.getMessage());
		}

		return bloodSugarData;
	}

	private Map<String, Object> getBloodPressureData(Long patientId, Integer count, Double sysMin, Double sysMax,
			Double diaMin, Double diaMax) {
		Map<String, Object> bloodPressureData = new HashMap<>();

		try {
			// Get systolic blood pressure data
			Map<String, Object> systolicData = getVitalTypeData(patientId, Constants.VitalTypes.SYSTOLIC_BLOOD_PRESSURE,
					count, sysMin, sysMax);

			// Get diastolic blood pressure data
			Map<String, Object> diastolicData = getVitalTypeData(patientId,
					Constants.VitalTypes.DIASTOLIC_BLOOD_PRESSURE, count, diaMin, diaMax);

			bloodPressureData.put("systolic", systolicData);
			bloodPressureData.put("diastolic", diastolicData);

		} catch (Exception e) {
			log.severe("Error getting blood pressure data: " + e.getMessage());
		}

		return bloodPressureData;
	}

	private Map<String, Object> getPedometerData(Long patientId, Integer count, Long min, Long max) {
		Map<String, Object> pedometerData = new HashMap<>();
		List<Map<String, Object>> values = new ArrayList<>();

		try {
			// Use PedometerReadingsDAO to get pedometer data
			List<WatchRxPatientPedometerReadings> pedometerReadings = pedometerReadingsDAO
					.getAllPedometerReadings(patientId, 0, count);

			for (WatchRxPatientPedometerReadings reading : pedometerReadings) {
				Map<String, Object> valueEntry = new HashMap<>();
				Long stepCount = reading.getCount();
				// Steps are already integers, so just convert Long to Integer
				Integer intValue = stepCount != null ? stepCount.intValue() : 0;
				valueEntry.put("value", intValue);
				valueEntry.put("datetime", dateFormatter.format(reading.getCreatedDate()));
				values.add(valueEntry);

			}

		} catch (Exception e) {
			log.severe("Error getting pedometer data: " + e.getMessage());
		}

		pedometerData.put("values", values);
		// Convert min and max to integers
		pedometerData.put("min", min != null ? (int) Math.round(min) : null);
		pedometerData.put("max", max != null ? (int) Math.round(max) : null);

		return pedometerData;
	}

	@Override
	public Map<String, Object> getLatest5VitalsForType(Long patientId, String vitalType) {
		// TODO Auto-generated method stub
		return null;
	}
}