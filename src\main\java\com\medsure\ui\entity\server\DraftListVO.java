package com.medsure.ui.entity.server;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DraftListVO extends StatusVO implements Serializable {

    private static final long serialVersionUID = 1L;
    private List<DraftVO> DraftList = new ArrayList<DraftVO>();
    private Long resultCount;

    private List<Map<String, Object>> programs;

    public Long getResultCount() {
        return resultCount;
    }

    public void setResultCount(Long resultCount) {
        this.resultCount = resultCount;
    }

    public List<DraftVO> getDraftList() {
        return DraftList;
    }

    public void setDraftList(List<DraftVO> draftList) {
        DraftList = draftList;
    }

    public List<Map<String, Object>> getPrograms() {
        return programs;
    }

    public void setPrograms(List<Map<String, Object>> programs) {
        this.programs = programs;
    }

}
