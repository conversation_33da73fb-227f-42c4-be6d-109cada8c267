package com.medsure.ui.entity.server;

import java.io.Serializable;
import java.util.List;

public class FaxRequest implements Serializable {

	private static final long serialVersionUID = 1L;

	private String startDate;
	private String endDate;
	private List<Long> patientId;
	private Long orgId;


	public String getStartDate() {
		return startDate;
	}

	public void setStartDate(String startDate) {
		this.startDate = startDate;
	}

	public String getEndDate() {
		return endDate;
	}

	public void setEndDate(String endDate) {
		this.endDate = endDate;

	}

	public List<Long> getPatientId() {
		return patientId;
	}

	public void setPatientId(List<Long> patientId) {
		this.patientId = patientId;
	}


	public Long getOrgId() {
		return orgId;
	}

	public void setOrgId(Long orgId) {
		this.orgId = orgId;
	}




}