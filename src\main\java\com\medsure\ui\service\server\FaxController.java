package com.medsure.ui.service.server;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medsure.common.Constants;
import com.medsure.fax.EncounterDataFormer;
import com.medsure.ui.entity.server.FaxRequest;
import com.medsure.ui.entity.server.StatusVO;
import com.medsure.ui.entity.server.UserVO;
import com.medsure.util.AESUtil;

@Controller
@RequestMapping("/service/fax")
public class FaxController {
	
	@Autowired
	EncounterDataFormer encounterDataFormer;
	
	@RequestMapping(value = "/sendFax", method = RequestMethod.POST)
	public @ResponseBody StatusVO sendFax(HttpServletRequest request,
			@RequestBody FaxRequest requestVo) throws Exception {
		StatusVO statusVO = new StatusVO();
		UserVO user = new ObjectMapper()
				.readValue(AESUtil.decryptText(request.getHeader("userdetails"), "patient@watCRX"), UserVO.class);
		if (user == null) {
			statusVO.setResponseCode(Constants.ResponseCode.UNAUTHORIZEDACCESS);
			String[] messages = new String[1];
			messages[0] = Constants.ResponseString.UNAUTHORIZEDACCESS;
			statusVO.setMessages(messages);
			statusVO.setSuccess(false);
			return statusVO;
		}
		encounterDataFormer.getEncounterdata(requestVo);
		statusVO.setResponseCode(Constants.ResponseCode.LOGINSUCCESS);
		String[] messages = new String[1];
		messages[0] = Constants.ResponseString.FAXSUCCESS;
		statusVO.setMessages(messages);
		statusVO.setSuccess(true);
		return statusVO;
	}
	
	@RequestMapping(value = "/scheduleFax", method = RequestMethod.POST)
	public @ResponseBody Void sendFaxToAll()  {
		encounterDataFormer.getEncounterdataForAll();
		return null;
	}

}
