package com.medsure.util;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import com.google.api.gax.longrunning.OperationFuture;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.speech.v1.LongRunningRecognizeMetadata;
import com.google.cloud.speech.v1.LongRunningRecognizeResponse;
import com.google.cloud.speech.v1.RecognitionAudio;
import com.google.cloud.speech.v1.RecognitionConfig;
import com.google.cloud.speech.v1.SpeechClient;
import com.google.cloud.speech.v1.SpeechRecognitionAlternative;
import com.google.cloud.speech.v1.SpeechRecognitionResult;
import com.google.cloud.speech.v1.SpeechSettings;
import com.google.cloud.vertexai.VertexAI;
import com.google.cloud.vertexai.api.Content;
import com.google.cloud.vertexai.api.GenerateContentResponse;
import com.google.cloud.vertexai.api.GenerationConfig;
import com.google.cloud.vertexai.generativeai.GenerativeModel;
import com.google.cloud.vertexai.generativeai.ResponseStream;
import com.google.protobuf.ByteString;
import com.medsure.model.WatchrxAudio;
import com.medsure.service.impl.PatientServiceImpl;

public class MedicalAudioSummarizer {
    private static final Logger logger = LoggerFactory.getLogger(PatientServiceImpl.class);
    
    private static final Pattern FOOTER_PATTERN = Pattern.compile("— Escalation: (EMERGENT|URGENT|ROUTINE|NONE)$");
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\b(\\d{2,4})\\b");
    private static final Pattern QUOTE_PATTERN = Pattern.compile("\"([^\"]{3,200})\"");
    
    private static final List<String> FORBIDDEN_PHRASES = Arrays.asList(
        "suggestive of", "likely", "rule out", "possible infection", 
        "diagnosis", "prognosis", "recommend changing medication"
    );
    
    public enum Escalation {
        EMERGENT, URGENT, ROUTINE, NONE
    }
    
    public static class ValidationException extends Exception {
        public ValidationException(String message) {
            super(message);
        }
    }

    public String summarizeData(String audioPath, WatchrxAudio audio) throws Exception {
        String projectId = "watchrx-1007";
        Resource resource = new ClassPathResource("secrets/watchrx-1007-cecd894ddd24.json");
        File file = resource.getFile();
        String absolutePath = file.getAbsolutePath();

        String transcript = transcribeAudio(audioPath, absolutePath);

        audio.setAudioText(transcript.trim());

        if (transcript == null || transcript.trim().isEmpty()) {
            logger.warn("No transcript found in audio file, skipping summarization");
            String noTranscriptMessage = "No transcript available - audio file did not contain recognizable speech";
            audio.setSummarizeAudioText(noTranscriptMessage);

            String validationChecksJson = "{\"hasTranscript\":\"false\",\"overallValid\":\"false\"}";
            audio.setValidationChecks(validationChecksJson);

            return noTranscriptMessage;
        }

        String summary = summarizeText(transcript, projectId, absolutePath);

        // Check if the model identified the transcript as invalid/non-medical
        if (summary.startsWith("INVALID_TRANSCRIPT:")) {
            logger.warn("Model identified non-medical transcript: {}", summary);
            audio.setSummarizeAudioText(summary);

            // Set validation checks to indicate invalid transcript
            String validationChecksJson = "{\"isValidMedicalTranscript\":\"false\",\"overallValid\":\"false\"}";
            audio.setValidationChecks(validationChecksJson);

            return summary;
        }

        String validationChecksJson = validateSummary(summary, transcript);

        audio.setValidationChecks(validationChecksJson);
        audio.setSummarizeAudioText(summary.trim());
        return summary;
    }

    public static String transcribeAudio(String audioPath, String keyPath) throws Exception {
        SpeechSettings settings = SpeechSettings.newBuilder()
                .setCredentialsProvider(() ->
                        ServiceAccountCredentials.fromStream(new FileInputStream(keyPath)))
                .build();

        try (SpeechClient speechClient = SpeechClient.create(settings)) {
            RecognitionConfig config = RecognitionConfig.newBuilder()
                    .setEncoding(RecognitionConfig.AudioEncoding.LINEAR16)
                    .setLanguageCode("en-US")
                    .setSampleRateHertz(16000)
                    .setModel("medical")
                    .setAudioChannelCount(2)
                    .build();

            RecognitionAudio audio;

            if (audioPath.startsWith("gs://")) {
                audio = RecognitionAudio.newBuilder()
                        .setUri(audioPath)
                        .build();
            } else {
                ByteString audioBytes;

                if (audioPath.startsWith("https://")) {
                    URL url = new URL(audioPath);
                    HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                    connection.setRequestMethod("GET");

                    try (InputStream inputStream = connection.getInputStream();
                         ByteArrayOutputStream buffer = new ByteArrayOutputStream()) {

                        byte[] data = new byte[4096];
                        int nRead;
                        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
                            buffer.write(data, 0, nRead);
                        }
                        audioBytes = ByteString.copyFrom(buffer.toByteArray());
                    }

                } else {
                    audioBytes = ByteString.copyFrom(Files.readAllBytes(Path.of(audioPath)));
                }

                audio = RecognitionAudio.newBuilder()
                        .setContent(audioBytes)
                        .build();
            }

            // Use LongRunningRecognize for all inputs
            OperationFuture<LongRunningRecognizeResponse, LongRunningRecognizeMetadata> response =
                    speechClient.longRunningRecognizeAsync(config, audio);

            LongRunningRecognizeResponse result = response.get(5, TimeUnit.MINUTES);

            StringBuilder transcript = new StringBuilder();
            for (SpeechRecognitionResult res : result.getResultsList()) {
                for (SpeechRecognitionAlternative alt : res.getAlternativesList()) {
                    transcript.append(alt.getTranscript()).append(" ");
                }
            }

            String finalTranscript = transcript.toString().trim();

            return finalTranscript;
        }
    }

    public static String summarizeText(String transcript, String projectId, String keyPath) throws Exception {
        String modelName = "gemini-2.5-flash";

        try (VertexAI vertexAI = new VertexAI(projectId, "us-central1")) {
            GenerationConfig generationConfig = GenerationConfig.newBuilder()
                    .setTemperature(0.8f)
                    .setMaxOutputTokens(10000)
                    .setTopP(0.95f)
                    .build();

            GenerativeModel model = new GenerativeModel.Builder()
                    .setModelName(modelName)
                    .setVertexAi(vertexAI)
                    .setGenerationConfig(generationConfig)
                    .build();

            logger.info("Sending prompt to Gemini model: " + modelName);
            String medicalPrompt = buildPrompt(transcript);

            Content promptContent = Content.newBuilder().setRole("user")
                    .addParts(com.google.cloud.vertexai.api.Part.newBuilder().setText(medicalPrompt).build())
                    .build();

            ResponseStream<GenerateContentResponse> responseStream = model.generateContentStream(promptContent);
            StringBuilder sb = new StringBuilder();
            for (GenerateContentResponse chunk : responseStream) {
                if (chunk.getCandidatesCount() > 0) {
                    for (com.google.cloud.vertexai.api.Part part : chunk.getCandidates(0).getContent().getPartsList()) {
                        if (part.hasText()) {
                            sb.append(part.getText());
                        }
                    }
                }
            }
            
            logger.info("FINAL_STRING_****{}", sb.toString());

            String finalResponse = sb.toString().trim();

            if (finalResponse.startsWith("INVALID_TRANSCRIPT:")) {
                logger.warn("Model identified invalid transcript: {}", finalResponse);
                return finalResponse;
            }

            return finalResponse;
        } catch (IOException e) {
            System.err.println("Error interacting with Vertex AI: " + e.getMessage());
            return e.getMessage();
        }
    }
    
    private static String buildPrompt(String transcript) {
        return String.format("""
            You are a Certified Medical Assistant (CMA) serving as a remote Care Manager for Chronic Care Management (CCM) and Remote Patient Monitoring (RPM), working under the supervision of the ordering provider.

            CRITICAL VALIDATION REQUIREMENTS - MUST BE FOLLOWED:
            1. **TRANSCRIPT VALIDATION FIRST** - Before processing, verify this is a medical patient-caregiver conversation
            2. **STRICT CONTENT BOUNDARIES** - If transcript contains ANY non-medical content, respond EXACTLY: "INVALID_TRANSCRIPT: Not a medical patient-caregiver conversation"
            3. **ZERO HALLUCINATION TOLERANCE** - Never generate, infer, or assume ANY information not explicitly stated in transcript
            4. **DOCUMENTATION ONLY** - You document what was said, you do NOT diagnose, treat, or provide medical advice
            5. **GROUNDING VERIFICATION** - Every statement in your response must be traceable to specific transcript content

            TRANSCRIPT VALIDATION CHECKLIST (ALL must be TRUE):
            ✓ Summary must be written from the caregiver perspective
            ✓ Contains patient discussing health concerns with caregiver/medical professional
            ✓ Includes medical terminology, symptoms, medications, vital signs, or care plans
            ✓ Represents a legitimate healthcare encounter or monitoring call
            ✗ NOT casual conversation, business call, technical support, entertainment, or non-medical content
            ✗ NOT unclear audio, garbled speech, or unintelligible content
            ✗ NOT test audio, sample recordings, or demonstration content

            ANTI-HALLUCINATION SAFEGUARDS:
            • **Source Attribution**: Every fact must reference transcript content ("Patient reports...", "I observed...", "Patient states...")
            • **Uncertainty Handling**: Use "information not provided" instead of guessing missing details
            • **Scope Limitation**: Only document this specific encounter, no historical assumptions
            • **Factual Grounding**: No medical knowledge beyond what patient/caregiver explicitly shared
            • **Context Boundaries**: Stay within the exact scope of the conversation

            If transcript passes ALL validation checks, create a clinical encounter summary:

            DOCUMENTATION REQUIREMENTS:
            • **Summary must be written from the caregiver perspective** - Write as if you are the healthcare professional documenting the encounter
            • Professional, concise, present-tense clinical documentation
            • Single flowing narrative paragraph (no bullet points or lists)
            • Standard medical terminology where appropriate
            • **ZERO clinical interpretations, diagnoses, or treatment recommendations**

            MANDATORY ELEMENTS (include ONLY if explicitly present in transcript):
            1. **Call Context** - Who initiated, when mentioned, stated purpose
            2. **Patient Statements** - Direct quotes with attribution ("Patient states: '...'")
            3. **Reported Status** - Symptoms, vital signs, device readings as patient described
            4. **Care Actions** - Only interventions actually performed during this call
            5. **Identified Barriers** - Care obstacles specifically mentioned by patient/caregiver
            6. **Escalation Assessment** - Assign ONLY if transcript clearly indicates:
                • **EMERGENT**: BP > 180/120, BG < 50 or > 400 mg/dL, severe SOB, chest pain, syncope, acute neurological changes
                • **URGENT**: BP ≥ 160/100 repeated, BG 300-400 mg/dL with symptoms, 2-5 lb CHF weight gain in 2-3 days, escalating COPD, missed dialysis
                • **ROUTINE**: Mild deviations or questions warranting provider review within 24-48 hours
                • **NONE**: No escalation criteria met based on transcript content
            7. **Follow-up Plans** - Only plans explicitly discussed and agreed upon
            8. **Provider Review Items** - Only concerns explicitly raised in conversation

            STRICT PROHIBITIONS - NEVER DO THESE:
            ✗ Generate medical information not in transcript
            ✗ Make clinical interpretations or diagnostic suggestions
            ✗ Add external medical knowledge or context
            ✗ Create fictional patient details, dates, or values
            ✗ Assume patient history not mentioned
            ✗ Provide medical advice or treatment recommendations
            ✗ Fill in gaps with "likely" or "probable" scenarios
            ✗ Reference medical guidelines not mentioned by participants

            REQUIRED OUTPUT FORMAT WITH EXAMPLES:

            EXAMPLE 1 - Valid Medical Transcript:
            Input: "Hi Mrs. Johnson, this is Sarah from the care team. How are you feeling today? I've been having some chest tightness and my blood pressure was 165 over 95 this morning. I took my medication as prescribed. Okay, let's schedule a follow-up call tomorrow to check on you."

            Output: "I initiated a routine check-in call with patient Mrs. Johnson who reported experiencing chest tightness and elevated blood pressure reading of 165/95 mmHg taken this morning, with patient confirming medication adherence as prescribed, and I scheduled a follow-up call for tomorrow to monitor patient's condition.
            — Escalation: URGENT"

            EXAMPLE 2 - Invalid Non-Medical Transcript:
            Input: "Hey, can you help me with my computer? It's running really slow and I can't open my email. Sure, let me walk you through some troubleshooting steps."

            Output: "INVALID_TRANSCRIPT: Not a medical patient-caregiver conversation"

            EXAMPLE 3 - Valid Medical Transcript with No Escalation:
            Input: "This is nurse Tom calling to check on your medication refill. Yes, I picked up my diabetes medication yesterday and I'm taking it twice daily as instructed. My blood sugar has been stable around 120. Great, continue with your current regimen."

            Output: "I conducted a medication adherence check-in with patient who confirmed picking up diabetes medication yesterday and reports taking it twice daily as instructed, with patient stating blood sugar levels have been stable around 120 mg/dL, and I advised patient to continue with current medication regimen.
            — Escalation: NONE"

            FINAL VERIFICATION: Before responding, confirm every statement in your summary can be directly traced to specific transcript content.

            Patient-Caregiver Conversation Transcript:
            %s""", transcript);
    }

    private static String validateSummary(String summary, String transcript) {

        if (summary == null || summary.trim().isEmpty()) {
            return "{\"validationError\":\"Summary is null or empty\",\"overallValid\":\"false\"}";
        }

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{");

        try {
            // 1. Check for invalid transcript response
            boolean isValidTranscript = !summary.startsWith("INVALID_TRANSCRIPT:");
            jsonBuilder.append("\"isValidTranscript\":\"").append(isValidTranscript).append("\",");

            if (!isValidTranscript) {
                jsonBuilder.append("\"overallValid\":\"false\"");
                jsonBuilder.append("}");
                return jsonBuilder.toString();
            }

            // 2. Footer + basic format validation
            boolean hasValidFooter = FOOTER_PATTERN.matcher(summary).find();
            jsonBuilder.append("\"hasValidFooter\":\"").append(hasValidFooter).append("\",");

            int footerIndex = summary.lastIndexOf("— Escalation:");
            if (footerIndex == -1) {
                footerIndex = summary.length();
            }

            String body = summary.substring(0, footerIndex);

            // 3. Single paragraph validation
            int newlineCount = body.length() - body.replace("\n", "").length();
            boolean isSingleParagraph = newlineCount <= 1;
            jsonBuilder.append("\"isSingleParagraph\":\"").append(isSingleParagraph).append("\",");

            // 4. Length validation
            boolean isValidLength = summary.length() <= 4096;
            jsonBuilder.append("\"isValidLength\":\"").append(isValidLength).append("\",");

            // 5. Quote validation (reuse existing helper method)
            String quoteIssue = checkQuotes(body, transcript);
            boolean hasValidQuotes = quoteIssue == null;
            jsonBuilder.append("\"hasValidQuotes\":\"").append(hasValidQuotes).append("\",");

            // 6. Number validation (reuse existing helper method)
            String numberIssue = checkNumbers(body, transcript);
            boolean hasValidNumbers = numberIssue == null;
            jsonBuilder.append("\"hasValidNumbers\":\"").append(hasValidNumbers).append("\",");

            // 7. Forbidden phrases check
            boolean hasNoForbiddenPhrases = true;
            for (String phrase : FORBIDDEN_PHRASES) {
                if (body.toLowerCase().contains(phrase.toLowerCase())) {
                    hasNoForbiddenPhrases = false;
                    break;
                }
            }
            jsonBuilder.append("\"hasNoForbiddenPhrases\":\"").append(hasNoForbiddenPhrases).append("\",");

            boolean overallValid = isValidTranscript && hasValidFooter && isSingleParagraph && isValidLength &&
                                  hasValidQuotes && hasValidNumbers && hasNoForbiddenPhrases;
            jsonBuilder.append("\"overallValid\":\"").append(overallValid).append("\"");

        } catch (Exception e) {
            logger.error("Summary validation error: {}", e.getMessage());
            jsonBuilder.append("\"validationError\":\"").append(e.getMessage()).append("\",");
            jsonBuilder.append("\"overallValid\":\"false\"");
        }

        jsonBuilder.append("}");

        String validationJson = jsonBuilder.toString();

        return validationJson;
    }
    
    private static String checkQuotes(String body, String transcript) {
        try {
            Matcher matcher = QUOTE_PATTERN.matcher(body);
            while (matcher.find()) {
                String quote = matcher.group(1).trim();
                String normalizedQuote = quote.replaceAll("\\s+", " ").toLowerCase();
                String normalizedTranscript = transcript.toLowerCase();
                
                if (normalizedQuote.length() >= 5 && !normalizedTranscript.contains(normalizedQuote)) {
                    return "Quote not in transcript";
                }
            }
            return null;
        } catch (Exception e) {
            logger.warn("Quote check error: {}", e.getMessage());
            return null;
        }
    }
    
    private static String checkNumbers(String body, String transcript) {
        try {
            Set<String> transcriptNumbers = new HashSet<>();
            Matcher transcriptMatcher = NUMBER_PATTERN.matcher(transcript);
            while (transcriptMatcher.find()) {
                transcriptNumbers.add(transcriptMatcher.group(1));
            }

            Matcher bodyMatcher = NUMBER_PATTERN.matcher(body);
            while (bodyMatcher.find()) {
                String number = bodyMatcher.group(1);
                if (Integer.parseInt(number) > 10 && !transcriptNumbers.contains(number)) {
                    return "Number " + number + " not in transcript";
                }
            }

            return null;
        } catch (Exception e) {
            logger.warn("Number check error: {}", e.getMessage());
            return null;
        }
    }
}