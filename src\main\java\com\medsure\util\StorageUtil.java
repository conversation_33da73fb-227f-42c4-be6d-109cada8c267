package com.medsure.util;

import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.util.concurrent.TimeUnit;

import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import com.google.appengine.api.appidentity.AppIdentityService;
import com.google.appengine.api.appidentity.AppIdentityServiceFactory;
import com.google.auth.oauth2.ServiceAccountCredentials;
import com.google.cloud.storage.BlobInfo;
import com.google.cloud.storage.Storage;
import com.google.cloud.storage.Storage.SignUrlOption;
import com.google.cloud.storage.StorageOptions;

public class StorageUtil {
	public static String downloadFile(String fileName) {
		URL signedUrl = null;
		try {
			if (fileName == null) {
				return "";
			}
			AppIdentityService appIdentityService = AppIdentityServiceFactory.getAppIdentityService();
			Storage storage = StorageOptions.getDefaultInstance().getService();
			storage.get(appIdentityService.getDefaultGcsBucketName(), fileName);

			Resource resource = new ClassPathResource("secrets/watchrx-1007-cecd894ddd24.json");
			File file = resource.getFile();
			String absolutePath = file.getAbsolutePath();
			signedUrl = storage.signUrl(
					BlobInfo.newBuilder(appIdentityService.getDefaultGcsBucketName(), fileName).build(), 1,
					TimeUnit.DAYS,
					SignUrlOption.signWith(ServiceAccountCredentials.fromStream(new FileInputStream(absolutePath))));
			System.out.println(signedUrl);
			return signedUrl.toString();
		} catch (Exception e) {
//			e.printStackTrace();
		}
		return "";
	}
}
