<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:mvc="http://www.springframework.org/schema/mvc"
	xmlns:rabbit="http://www.springframework.org/schema/rabbit"
	xsi:schemaLocation="http://www.springframework.org/schema/jee http://www.springframework.org/schema/jee/spring-jee-3.1.xsd
		http://www.springframework.org/schema/mvc http://www.springframework.org/schema/mvc/spring-mvc-3.1.xsd
		http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.1.xsd
		http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd
		http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		http://www.springframework.org/schema/rabbit https://www.springframework.org/schema/rabbit/spring-rabbit.xsd">
	<!-- This will automatically locate any and all property files you have 
		within your classpath, provided they fall under the META-INF/spring directory. 
		The located property files are parsed and their values can then be used within 
		application context files in the form of ${propertyKey}. -->
	<context:property-placeholder
		location="classpath*:META-INF/spring/*.properties" />
	<!-- Turn on AspectJ @Configurable support. As a result, any time you instantiate 
		an object, Spring will attempt to perform dependency injection on that object. 
		This occurs for instantiation via the "new" keyword, as well as via reflection. 
		This is possible because AspectJ is used to "weave" Roo-based applications 
		at compile time. In effect this feature allows dependency injection of any 
		object at all in your system, which is a very useful feature (without @Configurable 
		you'd only be able to dependency inject objects acquired from Spring or subsequently 
		presented to a specific Spring dependency injection method). Roo applications 
		use this useful feature in a number of areas, such as @PersistenceContext 
		injection into entities. -->
	<context:spring-configured />
	<!-- This declaration will cause Spring to locate every @Component, @Repository 
		and @Service in your application. In practical terms this allows you to write 
		a POJO and then simply annotate the new POJO as an @Service and Spring will 
		automatically detect, instantiate and dependency inject your service at startup 
		time. Importantly, you can then also have your new service injected into 
		any other class that requires it simply by declaring a field for your service 
		inside the relying class and Spring will inject it. Note that two exclude 
		filters are declared. The first ensures that Spring doesn't spend time introspecting 
		Roo-specific ITD aspects. The second ensures Roo doesn't instantiate your 
		@Controller classes, as these should be instantiated by a web tier application 
		context. Refer to web.xml for more details about the web tier application 
		context setup services. Furthermore, this turns on @Autowired, @PostConstruct 
		etc support. These annotations allow you to use common Spring and Java Enterprise 
		Edition annotations in your classes without needing to do any special configuration. 
		The most commonly used annotation is @Autowired, which instructs Spring to 
		dependency inject an object into your class. -->
	<context:component-scan
		base-package="com.medsure" />
	<context:component-scan
		base-package="com.medsure.dao.impl" />
	<context:component-scan
		base-package="com.medsure.service.impl" />
	<context:component-scan
		base-package="com.medsure.ui.service.server" />
	<context:component-scan
		base-package="com.medsure.ui.service.caregiver" />
	<context:component-scan
		base-package="com.medsure.ui.service.patient" />
	<context:component-scan
		base-package="com.medsure.ui.service.gcm" />
	<context:component-scan
		base-package="com.medsure.ui.interceptor" />

	<mvc:annotation-driven />

	<!-- <import resource="classpath*:META-INF/spring/tiles-context.xml" /> -->

	<bean id="watchRxFactory"
		class="com.medsure.factory.WatchRxFactory" />
	<bean id="dropdownUtils" class="com.medsure.util.DropdownUtils" />


	<bean id="dataSource"
		class="org.apache.commons.dbcp.BasicDataSource">

		<!-- local environment -->
		 <!--<property name="driverClassName" value="com.mysql.jdbc.Driver" /> 
			<property name="url" value="***********************************" /> -->
	<!--<property name="driverClassName"
			value="com.mysql.jdbc.Driver" /> 
	<property name="url" value="***************************************************"/>-->
		<!-- RPM Database -->
		<property name="driverClassName"
			value="com.mysql.jdbc.Driver" /> 
		<property name="url"
			value="*********************************************************************************************************************************************" />
    	<property name="username" value="root" />
		<property name="password" value="WatchRx@123" />

		 <!--<property name="password" value="root" /> -->
		<property name="initialSize" value="50" />
		<property name="maxActive" value="100" />
		<property name="maxIdle" value="2" />
		<property name="validationQuery" value="Select 1" />
		<property name="removeAbandonedTimeout" value="60" />
		<property name="removeAbandoned" value="true" />
		<property name="logAbandoned" value="true" /> 
	</bean>


	<bean class="org.springframework.orm.jpa.JpaTransactionManager"
		id="transactionManager">
		<property name="entityManagerFactory"
			ref="entityManagerFactory" />
	</bean>

	<tx:annotation-driven mode="aspectj"
		transaction-manager="transactionManager" />

	<bean
		class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean"
		id="entityManagerFactory">
		<property name="persistenceUnitName" value="watchRxJpa" />
		<property name="dataSource" ref="dataSource" />
	</bean>

	<bean
		class="org.springframework.context.support.ResourceBundleMessageSource"
		id="messageSource">
		<property name="basename" value="messages" />
	</bean>
	<!-- <bean id="multipartResolver" class="org.springframework.web.multipart.commons.CommonsMultipartResolver"> 
		<property name="maxUploadSize" value="-1" /> </bean> -->
	<bean id="multipartResolver"
		class="org.gmr.web.multipart.GMultipartResolver">
		<property name="maxUploadSize" value="524288000" />
	</bean>

	<bean id="sendMail" class="com.medsure.util.SendMail">
	</bean>

	<bean id="scheduledForMessage"
		class="com.medsure.config.ScheduledForMessage">
	</bean>
	
	<!-- local environment <rabbit:connection-factory id="connectionFactory" 
		host="localhost" port="15672"/> 
	<rabbit:connection-factory
		id="connectionFactory" host="************" port="15672" />

	<rabbit:template id="amqpTemplate"
		connection-factory="connectionFactory" />

	<rabbit:admin connection-factory="connectionFactory" />


	<bean id="connectionFactory"
		class="org.springframework.amqp.rabbit.connection.CachingConnectionFactory">

		<constructor-arg value="************" />


		<property name="username" value="user" />
		<property name="password" value="WatchRx@123" />
	</bean>
	<bean id="rabbitJsonConverter"
		class="org.springframework.amqp.support.converter.Jackson2JsonMessageConverter" />
	<rabbit:queue id="alertQueue" name="alertQueue" />
	<rabbit:queue id="vitalQueue" name="vitalQueue" />
	<rabbit:fanout-exchange name="watchrx_exchange"
		auto-delete="false" durable="true">
		<rabbit:bindings>
			<rabbit:binding queue="alertQueue" />
			<rabbit:binding queue="vitalQueue" />
		</rabbit:bindings>
	</rabbit:fanout-exchange>

	<bean id="alertMessageListener"
		class="com.medsure.config.listener.AlertMessageListener">
		<qualifier value="alertMessageListener" />
	</bean>
	<bean id="vitalMessageListener"
		class="com.medsure.config.listener.VitalMessageListener">
		<qualifier value="vitalMessageListener" />
	</bean>

	<rabbit:listener-container
		connection-factory="connectionFactory" acknowledge="auto"
		message-converter="rabbitJsonConverter">
		<rabbit:listener queues="alertQueue"
			ref="alertMessageListener" />
		<rabbit:listener queues="vitalQueue"
			ref="vitalMessageListener" />
	</rabbit:listener-container>-->


	<mvc:interceptors>
		<mvc:interceptor>
			<mvc:mapping path="/secure/**" />
			<mvc:mapping path="/service/**" />
			<bean
				class="com.medsure.ui.interceptor.AuthenticationInterceptor" />
		</mvc:interceptor>
	</mvc:interceptors>
</beans>
