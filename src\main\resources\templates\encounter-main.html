<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8" />
    <style>
        h1, h2 {
            text-align: center;
            margin-bottom: 10px;
            text-decoration: underline;
        }
         @page {
            margin-top: 100px;
            @top-center {
                content: element(header);
            }
        }

        @page:first {
            @top-center {
                content: none;
            }
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }

        .header {
            display: block;
            position: running(header);
            text-align: center;
            font-size: 12px;
            border-bottom: 1px solid #aaa;
            padding-bottom: 5px;
            margin-bottom: 20px;
        }

        .encounter {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
    </style>
</head>
<body>

<!-- Header to repeat on all pages except the first -->
<div class="header">
    <strong>Patient:</strong> <span th:text="${coversheet.patientName}"></span> |
    <strong>DOB:</strong> <span th:text="${coversheet.dob}"></span> |
    <strong>Address:</strong> <span th:text="${coversheet.address}"></span>
</div>
    <div th:insert="coversheet :: coversheetFragment"></div>
    <h1>Encounters</h1>
   <div th:each="enc : ${encounter}">
    <div th:insert="encounter :: encounterFragment(${enc})"></div>
</div>
</body>
</html>
