package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchInventoryDAO;
import com.medsure.model.WatchrxWatchInventory;

@Component
public class WatchInventoryDAOImpl extends BaseDAOImpl<WatchrxWatchInventory> implements WatchInventoryDAO {

	@Override
    @SuppressWarnings("unchecked")
	public boolean isExist(String make, String model, String color, String sellingPrice) {
		// TODO Auto-generated method stub

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventory> entities = (List<WatchrxWatchInventory>) em
//				.createQuery("FROM WatchrxWatchInventory WHERE makeAndModel='" + makeAndModel + "' AND color='" + color
				.createQuery("FROM WatchrxWatchInventory WHERE make='" + make + "' AND  model='" + model + "' AND color='" + color
						+ "' AND sellingPrice='" + sellingPrice + "'")
				.getResultList();
		if (entities.size() > 0)
			return true;
		else
			return false;
	}

	@Override
	@SuppressWarnings("unchecked")
	public WatchrxWatchInventory findByMakeModelColorPrice(String make, String model, String color, String sellingPrice) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventory> entities = (List<WatchrxWatchInventory>) em
//				.createQuery("FROM WatchrxWatchInventory WHERE makeAndModel='" + makeAndModel + "' AND color='" + color
				.createQuery("FROM WatchrxWatchInventory WHERE make='" + make + "' AND  model='" + model + "' AND color='" + color
						+ "' AND sellingPrice='" + sellingPrice + "'")
				.getResultList();
		if (entities.size() > 0)
			return entities.get(0);
		else
			return null;
	}
	
	@Override
    @SuppressWarnings("unchecked")
	public boolean isExistForModelDeviceName(String make, String model, String color, String devicename) {
	
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventory> entities = (List<WatchrxWatchInventory>) em
				.createQuery("FROM WatchrxWatchInventory WHERE make='" + make + "' AND  model='" + model + "'")
				.getResultList();
		if (entities.size() > 0)
			return true;
		else
			return false;
	}

}
