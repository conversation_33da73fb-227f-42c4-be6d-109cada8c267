package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchrxWatchAPKAssignmentDAO;
import com.medsure.model.WatchrxWatchAPKAssignment;

@Component
public class WatchrxWatchAPKAssignmentDAOImpl extends BaseDAOImpl<WatchrxWatchAPKAssignment>
		implements WatchrxWatchAPKAssignmentDAO {

	@Override
	public WatchrxWatchAPKAssignment findMostRecentWatchApkAssignmentByWatchId(Long watchId) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery(
				"SELECT e FROM WatchrxWatchAPKAssignment e WHERE e.watchrxWatch.watchId = :watchID ORDER BY e.updatedDate desc");

		query.setParameter("watchID", watchId);
		query.setMaxResults(1);

		if (query.getResultList().size() > 0) {
			return (WatchrxWatchAPKAssignment) query.getResultList().get(0);
		} else {
			return null;
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchAPKAssignment> findMostRecentWatchApkAssignment() {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery("SELECT e FROM WatchrxWatchAPKAssignment e WHERE e.isMostRecent = TRUE");
		return query.getResultList();

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatchAPKAssignment> findAllWatchApkAssignmentByWatchId(Long watchId) {
		// TODO Auto-generated method stub
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		Query query = em.createQuery(
				"SELECT e FROM WatchrxWatchAPKAssignment e WHERE e.watchrxWatch.watchId = :watchID ORDER BY e.updatedDate desc");
		query.setParameter("watchID", watchId);
		return query.getResultList();
	}

}