package com.medsure.dao.impl;

import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientPhoneCommunicationDAO;
import com.medsure.model.WatchRxPatientPhoneCommunication;

@Component
public class WatchRxPatientPhoneCommunicationDAOImpl extends BaseDAOImpl<WatchRxPatientPhoneCommunication>
		implements WatchRxPatientPhoneCommunicationDAO {
	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountWeekPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFWEEK(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId= :physicianId) "
						+ "AND YEAR(a.callStartTime) = YEAR(CURDATE()) AND WEEK(a.callStartTime) = WEEK(CURDATE())"
						+ "GROUP BY DAYOFWEEK(a.callStartTime)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountMonthPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId= :physicianId) "
						+ "AND YEAR(a.callStartTime) = YEAR(CURDATE()) AND MONTH(a.callStartTime) = MONTH(CURDATE()) "
						+ "GROUP BY DAYOFMONTH(a.callStartTime)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountYearPhysician(Long physicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId IN "
						+ "(SELECT b.patientId FROM WatchrxPatient b WHERE b.watchrxPhysician.physicianId= :physicianId) "
						+ "AND YEAR(a.callStartTime) = YEAR(CURDATE()) " + "GROUP BY MONTH(a.callStartTime)");
		q.setParameter("physicianId", physicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountYearCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a WHERE a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.callStartTime) = YEAR(CURDATE()) " + "GROUP BY MONTH(a.callStartTime)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountMonthCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.createdDate) = YEAR(CURDATE()) AND MONTH(a.callStartTime) = MONTH(CURDATE()) "
						+ "GROUP BY DAYOFMONTH(a.createdDate)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountWeekCaseManager(Long clinicianId) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFWEEK(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId IN "
						+ "(SELECT b.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt b WHERE b.watchrxClinician.clinicianId = :clinicianId)"
						+ "AND YEAR(a.callStartTime) = YEAR(CURDATE()) AND WEEK(a.callStartTime) = WEEK(CURDATE()) "
						+ "GROUP BY DAYOFWEEK(a.callStartTime)");
		q.setParameter("clinicianId", clinicianId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountMonth(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.callStartTime BETWEEN :startDate AND :endDate "
						+ "GROUP BY DAYOFMONTH(a.callStartTime)")
				.setParameter("startDate", startDate).setParameter("endDate", endDate);
		q.setParameter("patientId", patientId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> contactMinutesCountYear(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.callStartTime),SUM(a.duration) FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.callStartTime BETWEEN :startDate AND :endDate " + "GROUP BY MONTH(a.callStartTime)")
				.setParameter("startDate", startDate).setParameter("endDate", endDate);
		q.setParameter("patientId", patientId);
		List<Object[]> result = q.getResultList();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientPhoneCommunication> contactMinutesTimeIntervalPatient(Long patientId, Date startDate,
			Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT a FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.callStartTime BETWEEN :startDate AND :endDate ");
		q.setParameter("patientId", patientId).setParameter("startDate", startDate).setParameter("endDate", endDate);
		List<WatchRxPatientPhoneCommunication> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientPhoneCommunication> isDuplicateRecords(Long patientId, Date timeStamp) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = em.createQuery(
				"SELECT a FROM WatchRxPatientPhoneCommunication a WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.callStartTime = :timeStamp");
		q.setParameter("patientId", patientId).setParameter("timeStamp", timeStamp);
		List<WatchRxPatientPhoneCommunication> result = q.getResultList();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientPhoneCommunication> getMinsForMonth(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = em.createQuery(
				"SELECT a FROM WatchRxPatientPhoneCommunication a  WHERE a.watchrxPatient.patientId = :patientId "
						+ "AND YEAR(a.callStartTime) = YEAR(CURDATE()) AND MONTH(a.callStartTime) = MONTH(CURDATE())");
		q.setParameter("patientId", patientId);
		List<WatchRxPatientPhoneCommunication> result = q.getResultList();
		return result;
	}
}
