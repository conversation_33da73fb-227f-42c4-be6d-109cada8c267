package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxCalendarTaskDAO;
import com.medsure.model.WatchRxCalendarTask;
import com.medsure.model.WatchRxTasks;

@Component
public class WatchRxCalendarTaskDAOImpl extends BaseDAOImpl<WatchRxCalendarTask> implements WatchRxCalendarTaskDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxCalendarTask> getTaskByUserId(Long orgId, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxCalendarTask> result = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"Select c FROM WatchRxCalendarTask c WHERE c.watchrxUser.userId = :userId AND c.isDeleted = 0 "
							+ "AND c.watchrxPatient.patientId IN (SELECT p.patientId FROM WatchrxPatient p WHERE p.watchrxGroup.groupId = :orgId)");
			q.setParameter("userId", userId).setParameter("orgId", orgId);
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxCalendarTask> getTaskByUserIdAndDates(Long userId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxCalendarTask> result = new ArrayList<>();
		try {
			Query q = em.createQuery("Select c FROM WatchRxCalendarTask c WHERE c.watchrxUser.userId = :userId "
					+ "AND c.taskStartDateTime = :startDate AND c.taskEndDateTime = :endDate AND c.isDeleted = 0 ");
			q.setParameter("userId", userId).setParameter("startDate", startDate).setParameter("endDate", endDate);
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxCalendarTask> getTaskByDates(Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxCalendarTask> result = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"Select c FROM WatchRxCalendarTask c WHERE c.taskStartDateTime BETWEEN (:startDate) AND (:endDate)"
							+ " AND c.isDeleted = 0 AND c.isEmailSent = 0");
			q.setParameter("startDate", startDate).setParameter("endDate", endDate);
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxCalendarTask> getTaskByDatesAndUserId(Long orgId, Long userId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxCalendarTask> result = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"Select c FROM WatchRxCalendarTask c WHERE c.watchrxUser.userId = :userId AND c.isDeleted = 0 "
							+ " AND c.watchrxPatient.patientId IN (SELECT p.patientId FROM WatchrxPatient p WHERE p.watchrxGroup.groupId = :orgId) "
							+ " AND DATE(c.taskStartDateTime) = CURDATE() ORDER BY c.taskStartDateTime ASC");
			q.setParameter("userId", userId).setParameter("orgId", orgId);
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxCalendarTask> getTaskByDatesAndUserIdV2(Long orgId, Long userId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxCalendarTask> result = new ArrayList<>();
		try {
			Query q = em.createQuery(
					"Select c FROM WatchRxCalendarTask c WHERE c.watchrxUser.userId = :userId AND c.isDeleted = 0 "
							+ " AND c.watchrxPatient.patientId IN (SELECT p.patientId FROM WatchrxPatient p WHERE p.watchrxGroup.groupId = :orgId) "
							+ " AND c.taskStartDateTime < :endDate AND c.taskEndDateTime > :startDate"
							+ " ORDER BY c.taskStartDateTime ASC");
			q.setParameter("userId", userId).setParameter("orgId", orgId).setParameter("startDate", startDate).setParameter("endDate", endDate);
//			System.out.println("Sending to database:");
//			System.out.println("startDate (ms): " + startDate.getTime());
//			System.out.println("endDate (ms): " + endDate.getTime());
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxCalendarTask> getTasksByPatientId(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxCalendarTask> tasks = new ArrayList<>();
		try {
			tasks =  em.createQuery("SELECT e FROM WatchRxCalendarTask e WHERE e.watchrxPatient.patientId = :patientId AND e.watchrxPatient.status='Y' ")
						.setParameter("patientId", patientId).getResultList();			 
		} catch (Exception e) {
			e.printStackTrace();
		}
		return tasks;
	}
}
