package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchrxWatchInventoryTypeDAO;
import com.medsure.model.WatchrxWatchInventoryType;

@Component
public class WatchrxWatchInventoryTypeDAOImpl extends BaseDAOImpl<WatchrxWatchInventoryType>
		implements WatchrxWatchInventoryTypeDAO {

	@Override
	@SuppressWarnings("unchecked")
	public List<String> getAllWatchInventoryColors() {

		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + "watchinventorycolors" + "'")
				.getResultList();
		List<String> colors = new ArrayList<String>();
		String[] colorArray;
		for (WatchrxWatchInventoryType inventoryType : inventoryTypes) {
			colorArray = inventoryType.getValues().split(",");
			colors.addAll(new ArrayList<String>(Arrays.asList(colorArray)));
		}
		return colors;
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<String> getAllWatchInventoryModels() {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + "watchinventorymodels" + "'")
				.getResultList();
		List<String> colors = new ArrayList<String>();
		String[] colorArray;
		for (WatchrxWatchInventoryType inventoryType : inventoryTypes) {
			colorArray = inventoryType.getValues().split(",");
			colors.addAll(new ArrayList<String>(Arrays.asList(colorArray)));
		}
		return colors;
	}

	@Override
	@SuppressWarnings("unchecked")
	public Boolean doesWatchInventoryColorExist(String Color) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + "watchinventorycolors" + "'")
				.getResultList();
		List<String> colors = new ArrayList<String>();
		String[] colorArray;
		for (WatchrxWatchInventoryType inventoryType : inventoryTypes) {
			colorArray = inventoryType.getValues().split(",");
			colors.addAll(new ArrayList<String>(Arrays.asList(colorArray)));			
		}
		
		return colors.contains(Color.trim().toLowerCase());
	}

	@Override
	@SuppressWarnings("unchecked")
	public Boolean doesWatchInventoryModelExist(String Model) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + "watchinventorymodels" + "'")
				.getResultList();
		List<String> colors = new ArrayList<String>();
		String[] colorArray;
		for (WatchrxWatchInventoryType inventoryType : inventoryTypes) {
			colorArray = inventoryType.getValues().split(",");
			colors.addAll(new ArrayList<String>(Arrays.asList(colorArray)));			
		}
		
		return colors.contains(Model.trim().toLowerCase());
	}

	@Override
	@SuppressWarnings("unchecked")
	public List<String> getAllValues(String name) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + name.trim().toLowerCase() + "'")
				.getResultList();
		List<String> colors = new ArrayList<String>();
		String[] colorArray;
		for (WatchrxWatchInventoryType inventoryType : inventoryTypes) {
			colorArray = inventoryType.getValues().split(",");
			colors.addAll(new ArrayList<String>(Arrays.asList(colorArray)));			
		}
		
		return colors;
	}

	@Override
	@SuppressWarnings("unchecked")
	public Boolean doesNameExist(String name) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + name.trim().toLowerCase() + "'")
				.getResultList();
		if(inventoryTypes!=null && inventoryTypes.size()>0)
			return true;
		else
			return false;
	}

	@Override
	@SuppressWarnings("unchecked")
	public Boolean doesNameValueExist(String name, String Value) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatchInventoryType> inventoryTypes = (List<WatchrxWatchInventoryType>) em
				.createQuery("FROM WatchrxWatchInventoryType WHERE name='" + name.trim().toLowerCase() + "'")
				.getResultList();
		if(inventoryTypes!=null && inventoryTypes.size()>0) {
			List<String> colors = new ArrayList<String>();
			String[] colorArray;
			for (WatchrxWatchInventoryType inventoryType : inventoryTypes) {
				colorArray = inventoryType.getValues().split(",");
				colors.addAll(new ArrayList<String>(Arrays.asList(colorArray)));			
			}
			return colors.contains(Value.trim().toLowerCase());
		}else
			return false;
	}

}
