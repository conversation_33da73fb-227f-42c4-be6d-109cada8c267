package com.medsure.config.listener;

import java.io.IOException;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageListener;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medsure.factory.WatchRxFactory;
import com.medsure.ui.entity.patient.request.MedicationAlert;

public class AlertMessageListener implements MessageListener {

    @Override
    public void onMessage(Message message) {
        System.out.println("Got message: " + new String(message.getBody()));
        ObjectMapper mapper= new ObjectMapper();
        MedicationAlert info = new MedicationAlert();
		try {
			info = mapper.readValue(new String(message.getBody()), MedicationAlert.class);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		WatchRxFactory.getAlertService().alertRequestHandler(info);

    }

}
