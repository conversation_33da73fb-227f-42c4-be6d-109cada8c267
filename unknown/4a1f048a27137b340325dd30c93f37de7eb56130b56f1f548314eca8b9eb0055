package com.medsure.dao.impl;

import java.util.List;
import java.util.Set;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientProgramsDAO;
import com.medsure.model.WatchRxPatientPrograms;

@Component
public class WatchRxPatientProgramsDAOImpl extends BaseDAOImpl<WatchRxPatientPrograms>
		implements WatchRxPatientProgramsDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientPrograms> getByPatientIdAndProgramsId(Long patientId, Set<Long> programsId) {
		List<WatchRxPatientPrograms> result = null;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em
					.createQuery(
							"SELECT pp FROM WatchRxPatientPrograms pp WHERE pp.watchrxPatient.patientId= :patientId "
									+ "AND pp.watchRxMonitorPrograms.programId IN :programsId")
					.setParameter("patientId", patientId).setParameter("programsId", programsId);
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public WatchRxPatientPrograms getByPatientIdAndProgramId(Long patientId, Long programId) {
		WatchRxPatientPrograms result = null;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em
					.createQuery(
							"SELECT pp FROM WatchRxPatientPrograms pp WHERE pp.watchrxPatient.patientId= :patientId "
									+ "AND pp.watchRxMonitorPrograms.programId = :programId")
					.setParameter("patientId", patientId).setParameter("programId", programId);
			result = (WatchRxPatientPrograms) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPatientsCountByProgram(Integer role, Long clinicianId, Long orgId) {
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			List<Object[]> patientsList = null;
			try {
				if (role == 3) {
					patientsList = em.createNativeQuery(
							"SELECT mp.PROGRAM_ID, mp.PROGRAM_NAME, COUNT(distinct pp.FK_PATIENT_ID) AS patient_count"
									+ " FROM WATCHRX_MONITOR_PROGRAMS mp "
									+ " LEFT JOIN WATCHRX_PATIENT_PROGRAMS pp ON mp.PROGRAM_ID = pp.FK_PROGRAM_ID"
									+ " LEFT JOIN WATCHRX_PATIENT wp on wp.PATIENT_ID = pp.FK_PATIENT_ID"
									+ " WHERE pp.IS_ACTIVATED = 1 AND wp.STATUS = 'Y' AND GROUP_ID = :orgId "
									+ " GROUP BY mp.PROGRAM_ID, mp.PROGRAM_NAME ")
							.setParameter("orgId", orgId).getResultList();
				} else {
					patientsList = em.createNativeQuery(
							"SELECT mp.PROGRAM_ID, mp.PROGRAM_NAME, COUNT(distinct pp.FK_PATIENT_ID) AS patient_count"
									+ " FROM WATCHRX_MONITOR_PROGRAMS mp "
									+ " LEFT JOIN WATCHRX_PATIENT_PROGRAMS pp ON mp.PROGRAM_ID = pp.FK_PROGRAM_ID"
									+ " LEFT JOIN WATCHRX_PATIENT wp on wp.PATIENT_ID = pp.FK_PATIENT_ID"
									+ " WHERE pp.IS_ACTIVATED = 1 AND wp.STATUS = 'Y' AND GROUP_ID = :orgId AND wp.PATIENT_ID IN"
									+ " (SELECT FK_PATIENT_ID FROM WATCHRX_PATIENT_CLINICIAN_ASSIGNMNT WHERE FK_CLINICIAN_ID = :clinicianId)"
									+ " GROUP BY mp.PROGRAM_ID, mp.PROGRAM_NAME ")
							.setParameter("orgId", orgId).setParameter("clinicianId", clinicianId).getResultList();
				}
			} finally {
				em.close();
			}
			return patientsList;
		} catch (Exception e) {
			// TODO: handle exception
		}
		return null;
	}
}
