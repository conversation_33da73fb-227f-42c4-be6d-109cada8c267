package com.medsure.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class RabbitmqConfig {

/*	private String topicExchangeName = "alertExchange";

	private String queueName = "alertTest";

	@Bean(name = "queue")
	Queue queue() {

		System.out.println("queue created");
		return new Queue(queueName, false);
	}

	@Bean
	TopicExchange exchange() {
		return new TopicExchange(topicExchangeName);
	}

	@Bean
	Binding binding(Queue queue, TopicExchange exchange) {
		return BindingBuilder.bind(queue).to(exchange).with("WatchRx.alertTest");
	}

	@Bean
	SimpleMessageListenerContainer container(ConnectionFactory connectionFactory,
			MessageListenerAdapter listenerAdapter) {
		SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
		container.setConnectionFactory(connectionFactory);
		container.setQueueNames(queueName);
		container.setMessageListener(listenerAdapter);
		container.setBatchSize(1);
		container.setPrefetchCount(1);
		return container;
	}

	@Bean
	MessageListenerAdapter listenerAdapter(Receiver receiver) {
		MessageListenerAdapter messageListenerAdapter = new MessageListenerAdapter(receiver, "alertRequestHandler");
		
		messageListenerAdapter.containerAckMode(AcknowledgeMode.MANUAL);
		return  messageListenerAdapter;
	}*/

}