package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.common.Constants;
import com.medsure.dao.WatchDAO;
import com.medsure.model.WatchrxWatch;

/**
 * <AUTHOR>
 *
 */
@Component
public class WatchDAOImpl extends BaseDAOImpl<WatchrxWatch> implements WatchDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatch> getAll() {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatch> entities;
		try {
			entities = em
					.createQuery("FROM WatchrxWatch WHERE status='" + Constants.Status.ACTIVE + "'").getResultList();
		} finally {
			em.close();
		}
		return entities;
	}

	/**
	 * Gets a entity by property id
	 */
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatch> findByProperty(String propertyName, Object value) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatch> list;
		try {
			Query query = em.createQuery("SELECT e FROM WatchrxWatch e WHERE status='" + Constants.Status.ACTIVE
					+ "' AND e." + propertyName + "= " + value);
			list = query.getResultList();
		} finally {
			em.close();
		}
		return list;
	}

	@Override
	public Boolean doesImeiOrNumberExist(String imei, String number) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query query = em.createQuery("SELECT e FROM WatchrxWatch e WHERE e.watchImeiNumber" + "='" + imei
					+ "' OR e.phoneNumber='" + number + "'");
			if (query.getResultList() != null && query.getResultList().size() > 0) {
				return true;
			} else {
				return false;
			}
		} finally {
			em.close();
		}

	}

	@Override
	public Boolean doesImeiExist(String imei) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query query = em.createQuery("SELECT e FROM WatchrxWatch e WHERE e.status='" + Constants.Status.ACTIVE
					+ "' AND e.watchImeiNumber ='" + imei + "'");
			if (query.getResultList() != null && query.getResultList().size() > 0) {
				return true;
			} else {
				return false;
			}
		} finally {
			em.close();
		}

	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatch> findByImei(String imei) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatch> list;
		try {
			Query query = em.createQuery("SELECT e FROM WatchrxWatch e WHERE e.status='" + Constants.Status.ACTIVE
					+ "' AND e.watchImeiNumber ='" + imei + "'");
			list = query.getResultList();
		} finally {
			em.close();
		}
		return list;
	}
	
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchrxWatch> findByImeiLast(String imei) {
		// TODO Auto-generated method stub
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchrxWatch> list;
		try {
			Query query = em.createQuery("SELECT e FROM WatchrxWatch e WHERE e.status='" + Constants.Status.ACTIVE
					+ "' AND e.watchImeiNumber LIKE concat('%',:imei,'%') ")
					.setParameter("imei", imei);
			list = query.getResultList();
		} finally {
			em.close();
		}
		return list;
	}
}