package com.medsure.dao.impl;

import java.math.BigInteger;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientRpmCcmDetailsDAO;
import com.medsure.model.WatchRxPatientRpmCcmDetails;

@Component
public class WatchRxPatientRpmCcmDetailsDAOImpl extends BaseDAOImpl<WatchRxPatientRpmCcmDetails>
		implements WatchRxPatientRpmCcmDetailsDAO {

	@Override
	public BigInteger getPatientsCountByReviewType(Date startDate, Date endDate, Long orgId, Long clinicianId,
			Integer minutes, String reviewType) {
		EntityManager em = entityManagerFactory.createEntityManager();
		BigInteger count = null;
		try {
			Query q = em.createNativeQuery("SELECT COUNT(DISTINCT patient_id) AS patient_count "
					+ " FROM WATCHRX_PATIENT_RPM_CCM_DETAILS_VIEW v WHERE org_id =:orgId AND CLINICIAN_ID =:clinicianId AND "
					+ " ((CALL_START_TIME BETWEEN :startDate AND :endDate ) OR "
					+ " (doc_documented_date BETWEEN :startDate AND :endDate) OR "
					+ " (encounter_start_date BETWEEN :startDate AND :endDate)) GROUP BY patient_id HAVING"
					+ " SUM(CASE WHEN review_type =:reviewType THEN total_duration ELSE 0 END) < :minutes");
			q.setParameter("clinicianId", clinicianId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endDate);
			q.setParameter("orgId", orgId);
			q.setParameter("minutes", minutes);
			q.setParameter("reviewType", reviewType);
			count = (BigInteger) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return count;
	}

	@Override
	public BigInteger getPatientsCountByReviewTypeMinutesRange(Date startDate, Date endDate, Long orgId,
			Long clinicianId, Integer stMinutes, Integer enMinutes, String reviewType) {

		EntityManager em = entityManagerFactory.createEntityManager();
		BigInteger count = null;
		try {
			Query q = em.createNativeQuery("SELECT COUNT(DISTINCT patient_id) AS patient_count "
					+ " FROM WATCHRX_PATIENT_RPM_CCM_DETAILS_VIEW v WHERE org_id =:orgId AND CLINICIAN_ID =:clinicianId AND "
					+ " ((CALL_START_TIME BETWEEN :startDate AND :endDate ) OR "
					+ " (doc_documented_date BETWEEN :startDate AND :endDate) OR "
					+ " (encounter_start_date BETWEEN :startDate AND :endDate)) GROUP BY patient_id HAVING"
					+ " SUM(CASE WHEN review_type =:reviewType THEN total_duration ELSE 0 END) BETWEEN :stMinutes AND :enMinutes");
			q.setParameter("clinicianId", clinicianId);
			q.setParameter("startDate", startDate);
			q.setParameter("endDate", endDate);
			q.setParameter("orgId", orgId);
			q.setParameter("stMinutes", stMinutes);
			q.setParameter("stMinutes", stMinutes);
			q.setParameter("enMinutes", enMinutes);
			count = (BigInteger) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			em.close();
		}
		return count;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getPatientsRpmCcmPcmDetails(Date startDate, Date endDate, Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		List<Object[]> patientsList = null;
		try {
			patientsList = em.createNativeQuery("SELECT patient_id,"
					+ " SUM(CASE WHEN review_type = 'rpm' THEN total_duration ELSE 0 END) AS rpm_duration,"
					+ " SUM(CASE WHEN review_type = 'ccm' THEN total_duration ELSE 0 END) AS ccm_duration,"
					+ " SUM(CASE WHEN review_type = 'pcm' THEN total_duration ELSE 0 END) AS pcm_duration "
					+ " FROM WATCHRX_PATIENT_RPM_CCM_DETAILS_VIEW "
					+ " WHERE patient_id IN (:patientId) AND DATE(encounter_start_date) BETWEEN (:startDate) AND (:endDate) "
					+ " GROUP BY patient_id ").setParameter("patientId", patientId).setParameter("startDate", startDate)
					.setParameter("endDate", endDate).getResultList();
		} finally {
			em.close();
		}
		return patientsList;
	}
}
