package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.VitalDAO;
import com.medsure.model.WatchRxVitals;

@Component
public class VitalDAOImpl extends BaseDAOImpl<WatchRxVitals> implements VitalDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> vitalsCountMonth(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT DAYOFMONTH(a.vitalDatetime),AVG(a.systolicBloodPressure), AVG(a.diastolicBloodPressure) FROM WatchRxVitals a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.vitalDatetime BETWEEN :startDate AND :endDate "
						+ "GROUP BY DAYOFMONTH(a.vitalDatetime)")
				.setParameter("startDate", startDate).setParameter("endDate", endDate);
		q.setParameter("patientId", patientId);
		List<Object[]> result = q.getResultList();

		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> vitalsCountYear(Long patientId, Date startDate, Date endDate) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT MONTH(a.vitalDatetime),AVG(a.systolicBloodPressure), AVG(a.diastolicBloodPressure) FROM WatchRxVitals a  WHERE a.watchrxPatient.patientId= :patientId "
						+ "AND a.vitalDatetime BETWEEN :startDate AND :endDate " + "GROUP BY MONTH(a.vitalDatetime)")
				.setParameter("startDate", startDate).setParameter("endDate", endDate);
		q.setParameter("patientId", patientId);
		List<Object[]> result = q.getResultList();
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxVitals> vitalsOrderedByDateTime(Long patientId, Integer index, Integer pageSize) {

		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT a FROM WatchRxVitals a  WHERE a.watchrxPatient.patientId= :patientId ORDER BY a.vitalDatetime DESC").setFirstResult(index * pageSize).setMaxResults(pageSize)
				.setParameter("patientId", patientId);
		List<WatchRxVitals> result = q.getResultList();
		return result;
	}
	
	@Override
	public List<WatchRxVitals> vitalsOrderedByLatestDate(Long patientId) {
		Date today = new Date();
		Calendar cal = new GregorianCalendar();
		cal.setTime(today);
		cal.add(Calendar.DAY_OF_MONTH, -365);
		Date today30 = cal.getTime();
		EntityManager em = entityManagerFactory.createEntityManager();

		Query q = em.createQuery(
				"SELECT a FROM WatchRxVitals a  WHERE a.watchrxPatient.patientId= :patientId AND a.vitalDatetime BETWEEN :startDate AND :endDate ORDER BY a.vitalDatetime DESC")
				.setParameter("patientId", patientId).setParameter("startDate", today30).setParameter("endDate", today);
		@SuppressWarnings("unchecked")
		List<WatchRxVitals> result = q.getResultList()!=null? q.getResultList():new ArrayList<WatchRxVitals>();
		return result;
	}
	
}
