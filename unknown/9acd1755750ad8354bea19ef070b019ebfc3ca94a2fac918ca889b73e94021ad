package com.medsure.dao.impl;
import com.medsure.dao.WatchRxClinicianDeviceDAO;
import com.medsure.model.WatchRxClinicianDevice;
import org.springframework.stereotype.Component;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.List;
@Component
public class WatchRxClinicianDeviceDAOImpl extends BaseDAOImpl<WatchRxClinicianDevice> implements WatchRxClinicianDeviceDAO {
    @Override
    public List<WatchRxClinicianDevice> findByUserId(Long userId) {
        List<WatchRxClinicianDevice> result;
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Query query = em.createQuery(
                    "SELECT d FROM WatchRxClinicianDevice d WHERE d.clinicianId = :clinicianId");
            query.setParameter("clinicianId", userId);
            result = query.getResultList();
        } finally {
            em.close();
        }
        return result;
    }

    @Override
    public List<WatchRxClinicianDevice> findByGcmRegistrationId(String gcmRegistrationId) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Query query = em.createQuery(
                    "SELECT d FROM WatchRxClinicianDevice d WHERE d.gcmRegistrationId = :gcmId");
            query.setParameter("gcmId", gcmRegistrationId);

            return query.getResultList();
        } finally {
            em.close();
        }
    }

    public List<WatchRxClinicianDevice> findByUserIdAndGcmRegistrationId(Long userId, String gcmRegistrationId) {
        EntityManager em = entityManagerFactory.createEntityManager();
        try {
            Query query = em.createQuery(
                    "SELECT d FROM WatchRxClinicianDevice d " +
                            "WHERE d.clinicianId = :clinicianId " +
                            "AND d.gcmRegistrationId = :gcmId");
            query.setParameter("clinicianId", userId);
            query.setParameter("gcmId", gcmRegistrationId);
            return query.getResultList();
        } finally {
            em.close();
        }
    }
}
