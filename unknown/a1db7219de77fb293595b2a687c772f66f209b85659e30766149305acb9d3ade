package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientSpirometryReadingsDAO;
import com.medsure.model.WatchRxPatientSpirometryReadings;

@Component
public class WatchRxPatientSpirometryReadingsDAOImpl extends BaseDAOImpl<WatchRxPatientSpirometryReadings>
		implements WatchRxPatientSpirometryReadingsDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientSpirometryReadings> getAllReadingsByPatientIdAndPagination(Long patientId, Integer index,
			Integer pageSize) {
		List<WatchRxPatientSpirometryReadings> result = new ArrayList<>();
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			System.out.println("SpirometryReadings : " + patientId + " Index: " + index + " pageSize" + pageSize);
			result = em.createQuery(
					"SELECT a FROM WatchRxPatientSpirometryReadings a WHERE a.watchrxPatient.patientId = :patientId ORDER BY a.createdDate DESC")
					.setFirstResult(index * pageSize).setMaxResults(pageSize).setParameter("patientId", patientId)
					.getResultList();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public Long getCountReadingsByPatientId(Long patientId) {
		Long count = 0l;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query q = em.createQuery(
					"SELECT COUNT(a) FROM WatchRxPatientSpirometryReadings a WHERE a.watchrxPatient.patientId = :patientId ")
					.setParameter("patientId", patientId);
			count = (Long) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return count;
	}

	@Override
	public WatchRxPatientSpirometryReadings isDuplicateRecord(Long patientId, Date createdDate) {
		WatchRxPatientSpirometryReadings result = null;
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Query q = em.createQuery(
					"SELECT a FROM WatchRxPatientSpirometryReadings a WHERE a.watchrxPatient.patientId = :patientId AND a.createdDate = :createdDate")
					.setMaxResults(1).setParameter("patientId", patientId).setParameter("createdDate", createdDate);
			result = (WatchRxPatientSpirometryReadings) q.getSingleResult();
		} catch (Exception e) {
			System.out.println(" No Records:" + e.getMessage());
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<Object[]> getCountsForDateRange(Long patientId, Date startDate, Date endDate) {
		EntityManager em = entityManagerFactory.createEntityManager();
		Query q = null;
		List<Object[]> result = null;
		try {
			q = em.createNativeQuery("SELECT DATE(CREATED_DATE), COUNT(*)"
					+ " FROM medsure.WATCHRX_PATIENT_SPIROMETRY_READINGS WHERE FK_PATIENT_ID = :patientId"
					+ " AND DATE(CREATED_DATE) BETWEEN (:startDate) and (:endDate) GROUP BY DATE(CREATED_DATE)");
			q.setParameter("patientId", patientId).setParameter("startDate", startDate).setParameter("endDate",
					endDate);
			result = q.getResultList();
		} finally {
			em.close();
		}
		return result;
	}
}
