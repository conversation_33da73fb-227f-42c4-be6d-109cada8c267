package com.medsure.dao.impl;

import java.util.Date;
import java.util.List;
import java.util.Set;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Component;

import com.medsure.dao.TasksDAO;
import com.medsure.model.WatchRxTasks;

@Component
public class TasksDAOImpl extends BaseDAOImpl<WatchRxTasks> implements TasksDAO {
	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxTasks> getDueTasks(Date startDate, Long orgId, Integer roleType, Integer index, Integer pagesize,
			long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (roleType == 3) {
				List<WatchRxTasks> tasks = em
						.createQuery("SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
								+ " (SELECT a.patientId FROM WatchrxPatient a where a.watchrxPhysician.watchrxUser.userId = :userId)"
								+ " AND e.watchrxPatient.watchrxGroup.groupId =:orgId AND  e.watchrxPatient.status='Y' "
								+ " AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ORDER BY e.taskEndDate DESC")
						.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pagesize)
						.setMaxResults(pagesize).getResultList();
				return tasks;
			} else {
				List<WatchRxTasks> tasks = em.createQuery(
						"SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc"
								+ " where pc.watchrxClinician.watchrxUser.userId = :userId) AND e.watchrxPatient.watchrxGroup.groupId =:orgId AND  e.watchrxPatient.status='Y' "
								+ " AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ORDER BY  e.taskEndDate DESC")
						.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pagesize)
						.setMaxResults(pagesize).getResultList();
				return tasks;
			}
		} finally {
			em.close();
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxTasks> getfutureTasks(Date startDate, Long orgId, Integer roleType, Integer index,
			Integer pagesize, long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (roleType == 3) {
				List<WatchRxTasks> tasks = em
						.createQuery("SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
								+ " (SELECT a.patientId FROM WatchrxPatient a where a.watchrxPhysician.watchrxUser.userId = :userId) "
								+ " AND e.watchrxPatient.status='Y' AND e.watchrxPatient.watchrxGroup.groupId =:orgId "
								+ " AND DATE(e.taskEndDate) > CURRENT_TIMESTAMP() ORDER BY  e.taskEndDate DESC")
						.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pagesize)
						.setMaxResults(pagesize).getResultList();
				return tasks;
			} else {
				List<WatchRxTasks> tasks = em.createQuery(
						"SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc"
								+ " where pc.watchrxClinician.watchrxUser.userId = :userId) AND e.watchrxPatient.watchrxGroup.groupId =:orgId AND  e.watchrxPatient.status='Y' "
								+ " AND DATE(e.taskEndDate) > CURRENT_TIMESTAMP() ORDER BY  e.taskEndDate DESC")
						.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pagesize)
						.setMaxResults(pagesize).getResultList();
				return tasks;
			}
		} finally {
			em.close();
		}
	}

	@Override
	public Long getDueTasksCount(Long orgId, Integer roleType, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Object num = 0;
			if (roleType == 3) {
				num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
						+ " (SELECT a.patientId FROM WatchrxPatient a where a.watchrxPhysician.watchrxUser.userId = :userId )"
						+ " AND e.watchrxPatient.watchrxGroup.groupId =:orgId  AND  e.watchrxPatient.status='Y' "
						+ " AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP()").setParameter("userId", userId)
						.setParameter("orgId", orgId).getSingleResult();
				return (Long) num;
			} else {
				num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
						+ " (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc where pc.watchrxClinician.watchrxUser.userId = :userId)"
						+ " AND e.watchrxPatient.watchrxGroup.groupId =:orgId  AND  e.watchrxPatient.status='Y' "
						+ " AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP()").setParameter("userId", userId)
						.setParameter("orgId", orgId).getSingleResult();
				return (Long) num;
			}
		} finally {
			em.close();
		}
	}

	@Override
	public Long getFutureTasksCount(Long orgId, Integer roleType, long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Object num = 0;
			if (roleType == 3) {
				num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
						+ " (SELECT a.patientId FROM WatchrxPatient a where a.watchrxPhysician.watchrxUser.userId = :userId)"
						+ " AND e.watchrxPatient.watchrxGroup.groupId =:orgId  AND  e.watchrxPatient.status='Y' "
						+ " AND DATE(e.taskEndDate) > CURRENT_TIMESTAMP()").setParameter("userId", userId)
						.setParameter("orgId", orgId).getSingleResult();
				return (Long) num;
			} else {
				num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
						+ " (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc where pc.watchrxClinician.watchrxUser.userId = :userId)"
						+ " AND e.watchrxPatient.watchrxGroup.groupId =:orgId  AND  e.watchrxPatient.status='Y' "
						+ " AND DATE(e.taskEndDate) > CURRENT_TIMESTAMP()").setParameter("userId", userId)
						.setParameter("orgId", orgId).getSingleResult();
				return (Long) num;
			}
		} finally {
			em.close();
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxTasks> getDueTasksNotClosed(Date startDate, Set<Long> userId, Integer roleType, Integer index,
			Integer pagesize) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			if (roleType == 3) {
				List<WatchRxTasks> tasks = em.createQuery(
						"SELECT e FROM WatchRxTasks e WHERE e.taskStatus <> 'Closed' AND e.watchrxPatient.patientId IN (SELECT pc.patientId FROM WatchrxPatient pc"
								+ " where pc.watchrxPhysician.physicianId in (:userId)) "
								+ "AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ORDER BY  e.taskEndDate DESC")
						.setParameter("userId", userId).setFirstResult(index * pagesize).setMaxResults(pagesize)
						.getResultList();
				return tasks;
			} else {
				List<WatchRxTasks> tasks = em.createQuery(
						"SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc"
								+ " where pc.watchrxClinician.watchrxUser.userId = :userId) AND e.taskStatus <> 'Closed' AND "
								+ " e.watchrxPatient.patientId IN "
								+ " (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc where pc.watchrxClinician.clinicianId in (:userId)) AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ORDER BY  e.taskEndDate DESC")
						.setParameter("userId", userId).setParameter("userId", userId).setFirstResult(index * pagesize)
						.setMaxResults(pagesize).getResultList();
				return tasks;
			}
		} finally {
			em.close();
		}
	}

	@Override
	public Long getDueTasksNotClosedCount(Long orgId, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Object num = 0;
			num = em.createQuery(
					"SELECT count(e) FROM WatchRxTasks e WHERE e.taskStatus <> 'Closed' AND e.watchrxPatient.watchrxGroup.groupId =:orgId "
							+ " AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ")
					.setParameter("orgId", orgId).getSingleResult();
			return (Long) num;
		} finally {
			em.close();
		}
	}

	@Override
	public Long getDueTasksCountByName(Long userId, String taskName) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Object num = 0;

			num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN "
					+ " (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc where pc.watchrxClinician.clinicianId = :userId) AND e.taskTitle=:name AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ")
					.setParameter("userId", userId).setParameter("name", taskName).getSingleResult();
			return (Long) num;
		} finally {
			em.close();
		}
	}

	@Override
	public Long getDueTasksByDate(Long userId, Integer roleType, String date) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Object num = 0;
			if (roleType == 3) {
				num = em.createQuery(
						"SELECT count(e) FROM WatchRxTasks e WHERE e.taskStatus <> 'Closed' AND e.watchrxPatient.patientId IN (SELECT pc.patientId FROM WatchrxPatient pc"
								+ " where pc.watchrxPhysician.physicianId= :userId) "
								+ " AND e.taskEndDate > :date AND e.taskEndDate < :date")
						.setParameter("userId", userId).setParameter("date", date).getSingleResult();
				return (Long) num;
			} else {
				num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.taskStatus <> 'Closed' AND "
						+ " e.watchrxPatient.patientId IN (SELECT pc.watchrxPatient.patientId "
						+ " FROM WatchrxPatientClinicianAssignmnt pc where pc.watchrxClinician.clinicianId = :userId) "
						+ " AND e.taskEndDate > :date AND e.taskEndDate < :date").setParameter("userId", userId)
						.setParameter("date", date).getSingleResult();
				return (Long) num;
			}
		} finally {
			em.close();
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxTasks> getDueTasksNotClosedV1(Long orgId, Integer index, Integer pagesize, Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			List<WatchRxTasks> tasks = em.createQuery(
					"SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId IN (SELECT pc.watchrxPatient.patientId FROM WatchrxPatientClinicianAssignmnt pc "
							+ "where pc.watchrxClinician.watchrxUser.userId = :userId) AND e.taskStatus <> 'Closed' "
							+ " AND e.watchrxPatient.watchrxGroup.groupId =:orgId  AND e.watchrxPatient.status='Y'"
							+ "AND DATE(e.taskEndDate) <= CURRENT_TIMESTAMP() ORDER BY  e.taskEndDate DESC")
					.setParameter("userId", userId).setParameter("orgId", orgId).setFirstResult(index * pagesize)
					.setMaxResults(pagesize).getResultList();
			return tasks;
		} finally {
			em.close();
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxTasks> getAllTasksByPatientId(Integer index, Integer pagesize, Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			List<WatchRxTasks> tasks = em
					.createQuery("SELECT e FROM WatchRxTasks e WHERE e.watchrxPatient.patientId = :patientId AND "
							+ " e.watchrxPatient.status='Y' ORDER BY e.taskEndDate DESC , e.taskId DESC")
					.setParameter("patientId", patientId).setFirstResult(index * pagesize).setMaxResults(pagesize)
					.getResultList();
			return tasks;
		} finally {
			em.close();
		}
	}

	@Override
	public Long getAllTasksCount(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			Object num = 0;
			num = em.createQuery("SELECT count(e) FROM WatchRxTasks e WHERE e.watchrxPatient.patientId = :patientId "
					+ " AND e.watchrxPatient.status='Y'").setParameter("patientId", patientId).getSingleResult();
			return (Long) num;
		} finally {
			em.close();
		}
	}
}
