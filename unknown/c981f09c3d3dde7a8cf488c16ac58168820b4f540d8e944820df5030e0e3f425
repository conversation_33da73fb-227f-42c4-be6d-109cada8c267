package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;

import org.springframework.stereotype.Repository;

import com.medsure.dao.WatchRxPatientDiyvaCommunicationsDAO;
import com.medsure.model.WatchRxPatientDiyvaCommunications;
import com.medsure.ui.entity.diyva.DiyvaDialog;

@Repository
public class WatchRxPatientDiyvaCommunicationsDAOImpl extends BaseDAOImpl<WatchRxPatientDiyvaCommunications> implements WatchRxPatientDiyvaCommunicationsDAO {

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientDiyvaCommunications> getAllByPatientIdandDialogName(DiyvaDialog dialog) {
		System.out.println("In PatientDAOImpl:getAll"+dialog.getPatient()+" "+dialog.getWatchDialogId());
		EntityManager em = entityManagerFactory.createEntityManager();
		List<WatchRxPatientDiyvaCommunications> entities =  em.createQuery("SELECT e FROM WatchRxPatientDiyvaCommunications e WHERE e.watchrxPatient.patientId =:patientId and e.watchrxDialog.dialogId = :dialogId")
				.setParameter("patientId", dialog.getPatient()).setParameter("dialogId", dialog.getWatchDialogId()).getResultList();
		return entities;
	}	
	
}
