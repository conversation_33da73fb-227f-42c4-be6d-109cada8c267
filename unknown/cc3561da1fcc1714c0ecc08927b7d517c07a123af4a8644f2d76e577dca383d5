package com.medsure.dao.impl;

import java.util.Date;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxUserPasswordLinkDAO;
import com.medsure.model.WatchRxUserPasswordLink;

@Component
public class WatchRxUserPasswordLinkDAOImpl extends BaseDAOImpl<WatchRxUserPasswordLink>
		implements WatchRxUserPasswordLinkDAO {

	@Override
	public WatchRxUserPasswordLink getActiveLinkByUser(String userName) {
		EntityManager em = entityManagerFactory.createEntityManager();
		WatchRxUserPasswordLink passwordLink = null;
		try {
			Date twoHoursAgo = new Date(System.currentTimeMillis() - 30 * 60 * 1000);
			Query q = em.createQuery(
					"SELECT e FROM WatchRxUserPasswordLink e WHERE e.watchrxUser.userName =:userName AND e.createdDate >= :twoHoursAgo ORDER BY e.createdDate DESC");
			q.setParameter("userName", userName);
			q.setParameter("twoHoursAgo", twoHoursAgo);
			q.setMaxResults(1);
			passwordLink = (WatchRxUserPasswordLink) q.getSingleResult();
		} catch (Exception e) {
			System.out.println("Excpetion :" + e.getMessage());
		} finally {
			em.close();
		}
		return passwordLink;
	}

	@Override
	public WatchRxUserPasswordLink getLinkByToken(String token) {
		EntityManager em = entityManagerFactory.createEntityManager();
		WatchRxUserPasswordLink passwordLink = null;
		try {
			System.out.println("While getting Link Token: " + token);
			Query q = em.createQuery("SELECT e FROM WatchRxUserPasswordLink e WHERE e.token =:token");
			q.setParameter("token", token);
			q.setMaxResults(1);
			passwordLink = (WatchRxUserPasswordLink) q.getSingleResult();
		} catch (Exception e) {
			System.out.println("Excpetion :" + e.getMessage());
		} finally {
			em.close();
		}
		return passwordLink;
	}
}
