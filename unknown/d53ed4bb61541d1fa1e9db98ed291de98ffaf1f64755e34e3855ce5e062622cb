/**
 * 
 */
package com.medsure.dao.impl;

import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.medsure.dao.WatchrxChatDAO;
import com.medsure.model.WatchRxChat;

@Component
public class WatchRxChatDAOImpl extends BaseDAOImpl<WatchRxChat> implements WatchrxChatDAO {

	private static Logger logger = Logger.getLogger(WatchRxChatDAOImpl.class);

	@Transactional
	@Override
	public List<WatchRxChat> countOfUnreadMessage(Long userId, Long orgId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			Query query = em.createQuery(
					"SELECT a FROM WatchRxChat a where a.watchRxUser.userId = :userId and a.isRead = false and a.isSenderApp = true AND a.watchrxPatient.patientId In (Select p.patientId "
							+ " FROM WatchrxPatient p WHERE p.watchrxGroup.groupId = :orgId)");
			query.setParameter("userId", userId);
			query.setParameter("orgId", orgId);
			@SuppressWarnings("unchecked")
			List<WatchRxChat> result = query.getResultList();
			logger.info("result****" + result.size());
			return result;
		} catch (Exception e) {
			logger.info("error" + e.getMessage());
		} finally {
			em.close();
		}
		return null;
	}

	@Transactional
	@Override
	public void clearisReadToTrue(Long userId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			Query query = em
					.createQuery("UPDATE WatchRxChat a SET a.isRead = true where a.watchRxUser.userId = :userId");
			query.setParameter("userId", userId);
			query.executeUpdate();
			em.getTransaction().commit();
		} catch (Exception e) {
			logger.info("error" + e.getMessage());
		} finally {
			em.close();
		}
	}

	@Override
	public void clearUnreadMessageByPatientId(Long patientId) {
		EntityManager em = entityManagerFactory.createEntityManager();
		try {
			em.getTransaction().begin();
			Query query = em.createQuery(
					"UPDATE WatchRxChat a SET a.isRead = true where a.watchrxPatient.patientId = :patientId");
			query.setParameter("patientId", patientId);
			query.executeUpdate();
			em.getTransaction().commit();
		} catch (Exception e) {
			logger.info("error" + e.getMessage());
		} finally {
			em.close();
		}
	}
}
