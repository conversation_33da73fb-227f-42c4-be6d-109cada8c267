package com.medsure.config.listener;

import java.io.IOException;

import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageListener;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.medsure.factory.WatchRxFactory;
import com.medsure.ui.entity.server.VitalReadingVO;
@Component
public class VitalMessageListener implements MessageListener {

    @Override
    public void onMessage(Message message) {
    	 System.out.println("Got message: " + new String(message.getBody()));
         ObjectMapper mapper= new ObjectMapper();
         VitalReadingVO info = new VitalReadingVO();
 		try {
 			info = mapper.readValue(new String(message.getBody()), VitalReadingVO.class);
 		} catch (IOException e) {
 			// TODO Auto-generated catch block
 			e.printStackTrace();
 		}
        
        WatchRxFactory.getPatientService().saveVitalReadingsFromWatch(info);
    }
}
