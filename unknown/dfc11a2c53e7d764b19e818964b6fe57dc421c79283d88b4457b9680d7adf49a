package com.medsure.dao.impl;

import java.util.ArrayList;
import java.util.List;

import javax.persistence.EntityManager;
import javax.persistence.Query;

import org.springframework.stereotype.Component;

import com.medsure.dao.WatchRxPatientMedicationSummaryDAO;
import com.medsure.model.WatchRxPatientMedicationSummary;

@Component
public class WatchRxPatientMedicationSummaryDAOImpl extends BaseDAOImpl<WatchRxPatientMedicationSummary>
		implements WatchRxPatientMedicationSummaryDAO {

	@Override
	public WatchRxPatientMedicationSummary getMedicationIfoById(Long medicationId) {
		WatchRxPatientMedicationSummary result = null;
		try {
			EntityManager em = entityManagerFactory.createEntityManager();
			Query q = em.createQuery(
					"SELECT a FROM WatchRxPatientMedicationSummary a WHERE a.medicationSummaryId = :medicationId ");
			q.setParameter("medicationId", medicationId);
			result = (WatchRxPatientMedicationSummary) q.getSingleResult();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@SuppressWarnings("unchecked")
	@Override
	public List<WatchRxPatientMedicationSummary> getAllByPatientId(Long patientId) {
		List<WatchRxPatientMedicationSummary> result = new ArrayList<>();
		EntityManager em = null;
		try {
			em = entityManagerFactory.createEntityManager();
			String sql = "SELECT * FROM WATCHRX_PATIENT_MEDICATION_SUMMARY "
					+ "WHERE FK_PATIENT_ID = :patientId AND IS_ACTIVE = true ORDER BY UPADATED_DATE DESC";
			Query q = em.createNativeQuery(sql, WatchRxPatientMedicationSummary.class);
			q.setParameter("patientId", patientId);
			result = q.getResultList();
		} catch (Exception e) {
			e.printStackTrace(); // Replace with a logger
		} finally {
			if (em != null) {
				em.close();
			}
		}
		return result;
	}
}
